"""
Simple test to verify the enhanced error analysis functions work correctly.
"""

def test_enhanced_functions():
    """Test the enhanced error analysis functions."""
    try:
        print("Testing enhanced error analysis functions...")

        # Test imports
        from nodes.conversion_nodes import (
            extract_position_info,
            extract_dynamic_keywords_from_sql,
            classify_error_type_enhanced
        )
        print("✅ All imports successful")

        # Test dynamic keyword extraction
        sql = "SELECT id, name FROM users WHERE active = true"
        keywords = extract_dynamic_keywords_from_sql(sql)
        print(f"✅ Dynamic keywords extracted: {keywords}")

        # Test enhanced error classification
        error = "ERROR: syntax error at or near '(' at character 728"
        classification = classify_error_type_enhanced(error)
        print(f"✅ Error classified as: {classification['error_type']}")
        print(f"   Category: {classification['error_category']}")
        print(f"   Fix: {classification['suggested_fix']}")

        # Test position info extraction
        complex_error = "ERROR: syntax error at or near \"(\" at character 728\nLINE 139: SELECT xpath('//item[@id=\"123\"\n                               ^"
        error_info = extract_position_info(complex_error)
        print(f"✅ Position extracted: {error_info.get('position')}")
        print(f"   Line: {error_info.get('line')}")
        print(f"   Error type: {error_info.get('error_type')}")

        # Test more error types
        test_errors = [
            ("ERROR: relation \"users\" does not exist", "object_not_found"),
            ("ERROR: permission denied for table products", "permission_error"),
            ("ERROR: duplicate key value violates unique constraint", "constraint_violation"),
            ("ERROR: deadlock detected", "transaction_error"),
            ("ERROR: connection refused", "connection_error"),
            ("ERROR: division by zero", "data_error"),
            ("ERROR: feature not supported", "migration_error")
        ]

        print("\n🧪 Testing various error types:")
        for test_error, expected_type in test_errors:
            result = classify_error_type_enhanced(test_error)
            status = "✅" if result['error_type'] == expected_type else "❌"
            print(f"   {status} {expected_type}: {result['error_type']}")

        print("\n🎉 All tests passed! The enhanced functions are working correctly.")
        return True

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_enhanced_functions()
