# Standard library imports
import os
import re
from typing import Dict, Any, List, Tuple

# Third-party imports
import pandas as pd
import psycopg2
from dotenv import load_dotenv

# Local imports - State
from state import (
    WorkflowState, ErrorContext, StatementConversionOutput,
    ValidationOutput,
    Phase1IdentificationOutput, Phase2MappingOutput
)
from state.state import Phase1ErrorIdentificationOutput, Phase2ErrorContextOutput

# Local imports - Utilities
from formatting.sql_splitter import split_sql_statements

# Local imports - Prompts
from prompts.edge_case_enhanced_prompts import create_edge_case_validation_prompt
from prompts.error_validation_prompt import create_error_validation_prompt
from prompts.enhanced_source_mapping_prompt import create_error_statement_identification_prompt, create_sequential_mapping_prompt
from prompts.source_mapping_validation_prompt import create_source_mapping_validation_prompt
from prompts.statement_conversion_prompt import create_statement_conversion_prompt
from prompts.src_tgt_validation_prompt import create_src_tgt_validation_prompt
from prompts.two_phase_error_identification_prompt import create_phase1_error_identification_prompt, create_phase2_error_context_prompt

load_dotenv()


# Global counter to track the current iteration
CURRENT_ITERATION = 1

def get_output_path() -> str:
    """Get the output directory from environment variables."""
    output_path = os.getenv("OUTPUT_PATH", "output_files")
    os.makedirs(output_path, exist_ok=True)
    return output_path

def get_iteration_filename(base_filename: str) -> str:
    """Get a filename with iteration number."""
    # Use the global iteration counter
    global CURRENT_ITERATION

    # Log the iteration count being used for the filename
    print(f"📊 Using iteration count {CURRENT_ITERATION} for filename")

    # Split the filename and extension
    filename, extension = os.path.splitext(base_filename)

    # Return the filename with the iteration count
    return f"{filename}_iteration_{CURRENT_ITERATION}{extension}"


# Position-Based Identification Classes
class ErrorInfoExtractor:
    """Extract position and error information from PostgreSQL error messages."""

    @staticmethod
    def extract_position_info(error_message: str) -> Dict[str, Any]:
        """
        Extract comprehensive position/line information from PostgreSQL error.

        Args:
            error_message: PostgreSQL error message

        Returns:
            Dict containing position, line, error_text, context, and error_type
        """
        result = {
            'position': None,
            'line': None,
            'error_text': None,
            'error_context': None,
            'error_type': None,
            'error_category': None,
            'suggested_fix': None,
            'raw_message': error_message,
            'all_quoted_texts': [],
            'line_content': None
        }

        if not error_message:
            return result

        # Extract position information (Position: 728)
        # Match both 'Position:' and 'pos:' (case-insensitive, optional whitespace)
        position_match = re.search(r'(Position|pos):\s*(\d+)', error_message, re.IGNORECASE)
        if position_match:
            result['position'] = int(position_match.group(2))

        # Extract line information (LINE 139:)
        line_match = re.search(r'LINE\s+(\d+):', error_message, re.IGNORECASE)
        if line_match:
            result['line'] = int(line_match.group(1))

        # Extract ALL quoted text fragments for better context
        error_text_matches = re.findall(r'"([^"]*)"', error_message)
        if error_text_matches:
            result['all_quoted_texts'] = error_text_matches
            # Use the most meaningful quoted text (longest non-single-char)
            meaningful_texts = [text for text in error_text_matches if len(text.strip()) > 1]
            result['error_text'] = meaningful_texts[0] if meaningful_texts else error_text_matches[0]

        # Extract line content if available (after LINE X:)
        line_content_match = re.search(r'LINE\s+\d+:\s*(.+?)(?:\n|\^|$)', error_message, re.IGNORECASE | re.DOTALL)
        if line_content_match:
            result['line_content'] = line_content_match.group(1).strip()

        # Extract error context (text around the caret ^)
        context_match = re.search(r'(.{0,50})\^(.{0,50})', error_message)
        if context_match:
            result['error_context'] = {
            'before': context_match.group(1).strip(),
            'after': context_match.group(2).strip()
            }

        # Enhanced error type classification
        error_lower = error_message.lower()

        # Syntax errors
        if any(keyword in error_lower for keyword in [
            'syntax error', 'mismatched parentheses', 'unexpected', 'unterminated', 'parse error',
            'missing', 'invalid syntax', 'unrecognized', 'unexpected end of input', 'incomplete input']):
            result['error_type'] = 'syntax_error'
            result['error_category'] = 'parsing'
            if 'parentheses' in error_lower:
                result['suggested_fix'] = 'Check for missing or extra parentheses'
            elif 'unexpected' in error_lower:
                result['suggested_fix'] = 'Check for incorrect syntax or keywords'
            elif 'unterminated' in error_lower:
                result['suggested_fix'] = 'Check for unclosed quotes or comments'
            elif 'missing' in error_lower:
                result['suggested_fix'] = 'Check for missing keywords or symbols'
            else:
                result['suggested_fix'] = 'Check SQL syntax'

        # Object/function not found
        elif any(keyword in error_lower for keyword in [
            'does not exist', 'function', 'relation', 'column', 'table', 'sequence', 'view', 'index',
            'schema', 'operator', 'aggregate', 'type', 'cast', 'procedure', 'trigger', 'database', 'role'
        ]):
            result['error_type'] = 'object_not_found'
            result['error_category'] = 'schema'
            result['suggested_fix'] = 'Check object names, schema references, and existence'

        # Type mismatches
        elif any(keyword in error_lower for keyword in [
            'type', 'cannot cast', 'invalid input', 'type mismatch', 'cannot be cast', 'expected', 'found',
            'incompatible', 'wrong type', 'cannot convert', 'invalid byte sequence', 'invalid encoding'
        ]):
            result['error_type'] = 'type_mismatch'
            result['error_category'] = 'data_type'
            result['suggested_fix'] = 'Check data type compatibility and explicit casts'

        # Permission errors
        elif any(keyword in error_lower for keyword in [
            'permission denied', 'access denied', 'not authorized', 'must be owner', 'insufficient privilege',
            'cannot alter', 'cannot drop', 'cannot create', 'cannot grant', 'cannot revoke'
        ]):
            result['error_type'] = 'permission_error'
            result['error_category'] = 'security'
            result['suggested_fix'] = 'Check user permissions, roles, and privileges'

        # Constraint violations
        elif any(keyword in error_lower for keyword in [
            'constraint', 'violates', 'duplicate key', 'unique violation', 'not null violation',
            'foreign key violation', 'check violation', 'exclusion violation', 'primary key violation'
        ]):
            result['error_type'] = 'constraint_violation'
            result['error_category'] = 'data_integrity'
            result['suggested_fix'] = 'Check data constraints, uniqueness, and referential integrity'

        # Transaction/locking/deadlock errors
        elif any(keyword in error_lower for keyword in [
            'deadlock detected', 'could not obtain lock', 'serialization failure', 'transaction aborted',
            'in failed sql transaction', 'lock not available'
        ]):
            result['error_type'] = 'transaction_error'
            result['error_category'] = 'transaction'
            result['suggested_fix'] = 'Check transaction logic and locking'

        # Connection/network errors
        elif any(keyword in error_lower for keyword in [
            'connection refused', 'connection lost', 'timeout', 'server closed the connection',
            'could not connect', 'network is unreachable', 'connection reset'
        ]):
            result['error_type'] = 'connection_error'
            result['error_category'] = 'network'
            result['suggested_fix'] = 'Check database connection and network settings'

        # Encoding/locale errors
        elif any(keyword in error_lower for keyword in [
            'invalid byte sequence', 'invalid encoding', 'character not in repertoire', 'invalid locale'
        ]):
            result['error_type'] = 'encoding_error'
            result['error_category'] = 'encoding'
            result['suggested_fix'] = 'Check encoding and locale settings'

        # Data errors (overflow, division by zero, etc.)
        elif any(keyword in error_lower for keyword in [
            'division by zero', 'numeric value out of range', 'out of memory', 'cannot allocate memory',
            'value too long', 'invalid input syntax for', 'out of range', 'overflow', 'underflow'
        ]):
            result['error_type'] = 'data_error'
            result['error_category'] = 'data'
            result['suggested_fix'] = 'Check data values and calculations'

        # PL/pgSQL or procedural language errors
        elif any(keyword in error_lower for keyword in [
            'plpgsql', 'raise exception', 'raise notice', 'raise warning', 'raise log', 'raise info',
            'syntax error at or near', 'unexpected end of function', 'unexpected end of block'
        ]):
            result['error_type'] = 'plpgsql_error'
            result['error_category'] = 'plpgsql'
            result['suggested_fix'] = 'Check PL/pgSQL block structure and syntax'

        # Migration-specific or compatibility errors (Oracle to Postgres)
        elif any(keyword in error_lower for keyword in [
            'unsupported', 'not implemented', 'not supported', 'feature not supported',
            'oracle', 'compatibility', 'reserved keyword', 'invalid identifier', 'invalid character'
        ]):
            result['error_type'] = 'migration_error'
            result['error_category'] = 'compatibility'
            result['suggested_fix'] = 'Check for Oracle-specific features or reserved keywords'

        else:
            result['error_type'] = 'unknown'
            result['error_category'] = 'general'
            result['suggested_fix'] = 'Review error message for specific details'

        return result


class StatementPositionMapper:
    """Map statements to their positions in the original target code with enhanced accuracy."""

    @staticmethod
    def map_statements_to_positions(target_code: str, statements: List[str]) -> List[Dict[str, Any]]:
        """
        Map each statement to its position in the original target code with multiple fallback strategies.

        Args:
            target_code: Original target code string
            statements: List of split statements

        Returns:
            List of statement position mappings with enhanced accuracy
        """
        statement_positions = []
        current_pos = 0

        # Create normalized versions for better matching
        target_normalized = StatementPositionMapper._normalize_sql(target_code)

        for i, stmt in enumerate(statements, 1):
            stmt_clean = stmt.strip()
            if not stmt_clean:
                continue

            position_info = StatementPositionMapper._find_statement_position(
                target_code, target_normalized, stmt_clean, current_pos, i
            )

            statement_positions.append(position_info)

            # Update current position for next search
            if position_info['start_position'] != -1:
                current_pos = position_info['end_position']

        return statement_positions

    @staticmethod
    def _normalize_sql(sql_text: str) -> str:
        """Normalize SQL text for better matching by removing extra whitespace."""
        # Replace multiple whitespace with single space
        normalized = re.sub(r'\s+', ' ', sql_text)
        # Remove leading/trailing whitespace from lines
        normalized = '\n'.join(line.strip() for line in normalized.split('\n'))
        return normalized.strip()

    @staticmethod
    def _find_statement_position(target_code: str, target_normalized: str, stmt: str, start_pos: int, stmt_num: int) -> Dict[str, Any]:
        """
        Find statement position using multiple strategies with fallback options.

        Args:
            target_code: Original target code
            target_normalized: Normalized target code
            stmt: Statement to find
            start_pos: Starting position for search
            stmt_num: Statement number

        Returns:
            Position information dictionary
        """
        stmt_normalized = StatementPositionMapper._normalize_sql(stmt)

        # Strategy 1: Exact match in original code
        position = StatementPositionMapper._exact_match(target_code, stmt, start_pos)
        if position['found']:
            return StatementPositionMapper._create_position_info(
                stmt_num, position['start'], position['end'], target_code, stmt, 'exact_match'
            )

        # Strategy 2: Exact match in normalized code
        position = StatementPositionMapper._exact_match(target_normalized, stmt_normalized, 0)
        if position['found']:
            # Map back to original code positions
            original_pos = StatementPositionMapper._map_normalized_to_original(
                target_code, target_normalized, position['start'], position['end']
            )
            return StatementPositionMapper._create_position_info(
                stmt_num, original_pos['start'], original_pos['end'], target_code, stmt, 'normalized_match'
            )

        # Strategy 3: Fuzzy match with key phrases
        position = StatementPositionMapper._fuzzy_match(target_code, stmt, start_pos)
        if position['found']:
            return StatementPositionMapper._create_position_info(
                stmt_num, position['start'], position['end'], target_code, stmt, 'fuzzy_match'
            )

        # Strategy 4: Keyword-based positioning
        position = StatementPositionMapper._keyword_match(target_code, stmt, start_pos)
        if position['found']:
            return StatementPositionMapper._create_position_info(
                stmt_num, position['start'], position['end'], target_code, stmt, 'keyword_match'
            )

        # Strategy 5: Line-based estimation
        estimated_line = StatementPositionMapper._estimate_line_position(target_code, stmt_num, len(stmt.split('\n')))

        return {
            'statement_number': stmt_num,
            'start_position': -1,
            'end_position': -1,
            'start_line': estimated_line,
            'end_line': estimated_line + stmt.count('\n'),
            'content': stmt,
            'length': len(stmt),
            'match_method': 'estimation',
            'confidence': 0.1,
            'keywords': StatementPositionMapper._extract_keywords(stmt)
        }

    @staticmethod
    def _exact_match(text: str, pattern: str, start_pos: int) -> Dict[str, Any]:
        """Find exact match of pattern in text."""
        pos = text.find(pattern, start_pos)
        return {
            'found': pos != -1,
            'start': pos,
            'end': pos + len(pattern) if pos != -1 else -1
        }

    @staticmethod
    def _fuzzy_match(target_code: str, stmt: str, start_pos: int) -> Dict[str, Any]:
        """Find statement using fuzzy matching with key phrases."""
        # Extract key phrases (first 30 and last 30 characters)
        stmt_clean = stmt.strip()
        if len(stmt_clean) < 60:
            key_phrase = stmt_clean
        else:
            key_phrase = stmt_clean[:30] + ".*?" + re.escape(stmt_clean[-30:])

        try:
            match = re.search(key_phrase, target_code[start_pos:], re.DOTALL | re.IGNORECASE)
            if match:
                actual_start = start_pos + match.start()
                actual_end = start_pos + match.end()
                return {'found': True, 'start': actual_start, 'end': actual_end}
        except re.error:
            pass

        return {'found': False, 'start': -1, 'end': -1}

    @staticmethod
    def _keyword_match(target_code: str, stmt: str, start_pos: int) -> Dict[str, Any]:
        """Find statement using keyword-based matching."""
        keywords = StatementPositionMapper._extract_keywords(stmt)
        if not keywords:
            return {'found': False, 'start': -1, 'end': -1}

        # Look for the first keyword after start_pos
        for keyword in keywords:
            pos = target_code.find(keyword, start_pos)
            if pos != -1:
                # Estimate statement boundaries around the keyword
                stmt_start = max(0, pos - 100)
                stmt_end = min(len(target_code), pos + len(stmt) + 100)
                return {'found': True, 'start': stmt_start, 'end': stmt_end}

        return {'found': False, 'start': -1, 'end': -1}

    @staticmethod
    def _extract_keywords(stmt: str) -> List[str]:
        """Extract meaningful keywords from SQL statement."""
        # SQL keywords and identifiers
        sql_keywords = ['SELECT', 'UPDATE', 'INSERT', 'DELETE', 'CREATE', 'ALTER', 'DROP',
                       'DECLARE', 'BEGIN', 'END', 'CASE', 'WHEN', 'THEN', 'ELSE']

        words = re.findall(r'\b\w+\b', stmt.upper())
        keywords = []

        # Add SQL keywords found in statement
        for word in words:
            if word in sql_keywords:
                keywords.append(word)

        # Add unique identifiers (likely table/column names)
        unique_words = [word for word in words if len(word) > 3 and word not in sql_keywords]
        keywords.extend(unique_words[:3])  # Take first 3 unique words

        return keywords

    @staticmethod
    def _map_normalized_to_original(original: str, normalized: str, norm_start: int, norm_end: int) -> Dict[str, int]:
        """Map positions from normalized text back to original text."""
        # This is a simplified mapping - in practice, you'd need more sophisticated logic
        # For now, we'll estimate based on character ratios
        ratio = len(original) / len(normalized) if normalized else 1

        return {
            'start': int(norm_start * ratio),
            'end': int(norm_end * ratio)
        }

    @staticmethod
    def _estimate_line_position(target_code: str, stmt_num: int, stmt_lines: int) -> int:
        """Estimate line position based on statement number."""
        total_lines = target_code.count('\n') + 1
        estimated_line = max(1, int((stmt_num / 100) * total_lines))  # Rough estimation
        return estimated_line

    @staticmethod
    def _create_position_info(stmt_num: int, start_pos: int, end_pos: int, target_code: str,
                            stmt: str, method: str) -> Dict[str, Any]:
        """Create comprehensive position information."""
        if start_pos == -1:
            return {
                'statement_number': stmt_num,
                'start_position': -1,
                'end_position': -1,
                'start_line': -1,
                'end_line': -1,
                'content': stmt,
                'length': len(stmt),
                'match_method': method,
                'confidence': 0.0,
                'keywords': StatementPositionMapper._extract_keywords(stmt)
            }

        # Calculate line numbers
        lines_before = target_code[:start_pos].count('\n')
        lines_in_stmt = stmt.count('\n')

        # Calculate confidence based on method
        confidence_map = {
            'exact_match': 0.95,
            'normalized_match': 0.85,
            'fuzzy_match': 0.70,
            'keyword_match': 0.50,
            'estimation': 0.10
        }

        return {
            'statement_number': stmt_num,
            'start_position': start_pos,
            'end_position': end_pos,
            'start_line': lines_before + 1,
            'end_line': lines_before + lines_in_stmt + 1,
            'content': stmt,
            'length': len(stmt),
            'match_method': method,
            'confidence': confidence_map.get(method, 0.5),
            'keywords': StatementPositionMapper._extract_keywords(stmt)
        }


class PositionBasedIdentifier:
    """Identify error statements using enhanced position information and multiple strategies."""

    @staticmethod
    def identify_error_statement(error_info: Dict[str, Any], statement_positions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Identify the statement containing the error using comprehensive position analysis.

        Args:
            error_info: Enhanced error information extracted from error message
            statement_positions: Enhanced statement position mappings

        Returns:
            Comprehensive identification result with statement number and confidence
        """
        result = {
            'statement_number': None,
            'confidence': 0.0,
            'method': 'multi_strategy',
            'verification': False,
            'candidates': [],
            'reasoning': '',
            'error_analysis': {},
            'fallback_used': False
        }

        # Extract enhanced error information
        position = error_info.get('position')
        line = error_info.get('line')
        error_text = error_info.get('error_text')
        all_quoted_texts = error_info.get('all_quoted_texts', [])
        error_type = error_info.get('error_type')
        error_category = error_info.get('error_category')
        line_content = error_info.get('line_content')

        candidates = []

        # Strategy 1: Precise position-based identification
        if position is not None:
            candidates.extend(PositionBasedIdentifier._position_based_identification(
                position, statement_positions, error_info
            ))

        # Strategy 2: Enhanced line-based identification
        if line is not None:
            candidates.extend(PositionBasedIdentifier._line_based_identification(
                line, statement_positions, error_info
            ))

        # Strategy 3: Multi-text content matching
        if all_quoted_texts:
            candidates.extend(PositionBasedIdentifier._multi_content_identification(
                all_quoted_texts, statement_positions, error_info
            ))

        # Strategy 4: Error-type specific identification
        if error_type and error_category:
            candidates.extend(PositionBasedIdentifier._error_type_identification(
                error_type, error_category, statement_positions, error_info
            ))

        # Strategy 5: Syntax pattern identification
        if error_type == 'syntax_error':
            candidates.extend(PositionBasedIdentifier._syntax_pattern_identification(
                statement_positions, error_info
            ))

        # Strategy 6: Line content matching
        if line_content:
            candidates.extend(PositionBasedIdentifier._line_content_identification(
                line_content, statement_positions, error_info
            ))

        # Remove duplicates and sort by confidence
        unique_candidates = PositionBasedIdentifier._deduplicate_candidates(candidates)

        if unique_candidates:
            # Select best candidate with enhanced verification
            best_candidate = PositionBasedIdentifier._select_best_candidate(
                unique_candidates, statement_positions, error_info
            )

            result.update({
                'statement_number': best_candidate['statement_number'],
                'confidence': best_candidate['confidence'],
                'method': best_candidate['method'],
                'verification': best_candidate.get('verification', False),
                'candidates': unique_candidates,
                'reasoning': best_candidate['details'],
                'error_analysis': best_candidate.get('analysis', {}),
                'fallback_used': False
            })
        else:
            # Fallback: Use heuristic-based identification
            result.update(PositionBasedIdentifier._fallback_identification(
                statement_positions, error_info
            ))
            result['fallback_used'] = True

        return result

    @staticmethod
    def _position_based_identification(position: int, statement_positions: List[Dict[str, Any]],
                                     error_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Enhanced position-based identification with confidence scoring."""
        candidates = []

        for stmt_pos in statement_positions:
            if stmt_pos['start_position'] == -1:
                continue

            start_pos = stmt_pos['start_position']
            end_pos = stmt_pos['end_position']

            if start_pos <= position <= end_pos:
                # Calculate relative position within statement for confidence
                relative_pos = (position - start_pos) / (end_pos - start_pos) if end_pos > start_pos else 0

                # Higher confidence for errors near the beginning or end of statements
                position_confidence = 0.9
                if 0.1 <= relative_pos <= 0.9:  # Middle of statement
                    position_confidence = 0.95

                # Boost confidence based on statement mapping quality
                mapping_confidence = stmt_pos.get('confidence', 0.5)
                final_confidence = position_confidence * mapping_confidence

                candidates.append({
                    'statement_number': stmt_pos['statement_number'],
                    'method': 'precise_position',
                    'confidence': final_confidence,
                    'details': f"Position {position} at {relative_pos:.2f} within statement bounds {start_pos}-{end_pos}",
                    'verification': True,
                    'analysis': {
                        'relative_position': relative_pos,
                        'mapping_method': stmt_pos.get('match_method', 'unknown'),
                        'mapping_confidence': mapping_confidence
                    }
                })

        return candidates

    @staticmethod
    def _line_based_identification(line: int, statement_positions: List[Dict[str, Any]],
                                 error_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Enhanced line-based identification."""
        candidates = []

        for stmt_pos in statement_positions:
            if stmt_pos['start_line'] == -1:
                continue

            start_line = stmt_pos['start_line']
            end_line = stmt_pos['end_line']

            if start_line <= line <= end_line:
                # Calculate confidence based on line position within statement
                line_confidence = 0.85
                if line == start_line or line == end_line:
                    line_confidence = 0.9  # Higher confidence for boundary lines

                candidates.append({
                    'statement_number': stmt_pos['statement_number'],
                    'method': 'line_match',
                    'confidence': line_confidence,
                    'details': f"Line {line} falls within statement lines {start_line}-{end_line}",
                    'verification': True,
                    'analysis': {
                        'line_position': line - start_line,
                        'statement_lines': end_line - start_line + 1
                    }
                })

        return candidates

    @staticmethod
    def _multi_content_identification(all_quoted_texts: List[str], statement_positions: List[Dict[str, Any]],
                                    error_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Enhanced content matching using all quoted texts."""
        candidates = []

        for stmt_pos in statement_positions:
            content = stmt_pos['content']
            matches = []

            for quoted_text in all_quoted_texts:
                if quoted_text and len(quoted_text.strip()) > 1:  # Ignore single characters
                    if quoted_text in content:
                        matches.append(quoted_text)

            if matches:
                # Calculate confidence based on number and quality of matches
                confidence = min(0.8, 0.4 + (len(matches) * 0.2))

                # Boost confidence for longer, more specific matches
                avg_length = sum(len(match) for match in matches) / len(matches)
                if avg_length > 10:
                    confidence += 0.1

                candidates.append({
                    'statement_number': stmt_pos['statement_number'],
                    'method': 'multi_content_match',
                    'confidence': confidence,
                    'details': f"Found {len(matches)} content matches: {matches}",
                    'verification': True,
                    'analysis': {
                        'matched_texts': matches,
                        'match_count': len(matches),
                        'avg_match_length': avg_length
                    }
                })

        return candidates

    @staticmethod
    def _error_type_identification(error_type: str, error_category: str, statement_positions: List[Dict[str, Any]],
                                 error_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify statements based on error type patterns."""
        candidates = []

        # Define error type patterns
        type_patterns = {
            'syntax_error': [r'\(', r'\)', r'\[', r'\]', r'\{', r'\}', r';', r','],
            'object_not_found': [r'\b\w+\.\w+\b', r'\bFROM\s+\w+\b', r'\bJOIN\s+\w+\b'],
            'type_mismatch': [r'CAST\s*\(', r'::', r'CONVERT\s*\('],
            'constraint_violation': [r'INSERT\s+INTO', r'UPDATE\s+\w+\s+SET', r'PRIMARY\s+KEY', r'UNIQUE']
        }

        patterns = type_patterns.get(error_type, [])

        for stmt_pos in statement_positions:
            content = stmt_pos['content']
            pattern_matches = 0

            for pattern in patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    pattern_matches += 1

            if pattern_matches > 0:
                confidence = min(0.7, 0.3 + (pattern_matches * 0.2))

                candidates.append({
                    'statement_number': stmt_pos['statement_number'],
                    'method': 'error_type_pattern',
                    'confidence': confidence,
                    'details': f"Statement matches {pattern_matches} patterns for {error_type}",
                    'verification': False,
                    'analysis': {
                        'error_type': error_type,
                        'error_category': error_category,
                        'pattern_matches': pattern_matches
                    }
                })

        return candidates

    @staticmethod
    def _syntax_pattern_identification(statement_positions: List[Dict[str, Any]],
                                     error_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify syntax errors using pattern analysis."""
        candidates = []

        for stmt_pos in statement_positions:
            content = stmt_pos['content']
            syntax_issues = []

            # Check for common syntax issues
            open_parens = content.count('(')
            close_parens = content.count(')')
            if open_parens != close_parens:
                syntax_issues.append(f"Parentheses mismatch: {open_parens} open, {close_parens} close")

            # Check for unclosed quotes
            single_quotes = content.count("'") % 2
            double_quotes = content.count('"') % 2
            if single_quotes != 0 or double_quotes != 0:
                syntax_issues.append("Unclosed quotes detected")

            # Check for incomplete XPath expressions (specific to the current error)
            xpath_pattern = r"xpath\s*\(\s*'[^']*\(\s*'"
            if re.search(xpath_pattern, content, re.IGNORECASE):
                syntax_issues.append("Incomplete XPath expression detected")

            if syntax_issues:
                confidence = min(0.85, 0.5 + (len(syntax_issues) * 0.2))

                candidates.append({
                    'statement_number': stmt_pos['statement_number'],
                    'method': 'syntax_pattern',
                    'confidence': confidence,
                    'details': f"Syntax issues detected: {'; '.join(syntax_issues)}",
                    'verification': True,
                    'analysis': {
                        'syntax_issues': syntax_issues,
                        'parentheses_balance': open_parens - close_parens,
                        'quote_issues': single_quotes + double_quotes > 0
                    }
                })

        return candidates

    @staticmethod
    def _line_content_identification(line_content: str, statement_positions: List[Dict[str, Any]],
                                   error_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify statements based on line content from error message."""
        candidates = []

        if not line_content or len(line_content.strip()) < 3:
            return candidates

        # Normalize line content for matching
        normalized_line = re.sub(r'\s+', ' ', line_content.strip())

        for stmt_pos in statement_positions:
            content = stmt_pos['content']

            # Check if line content appears in statement
            if normalized_line in content:
                confidence = 0.9
            elif any(word in content for word in normalized_line.split() if len(word) > 3):
                confidence = 0.6
            else:
                continue

            candidates.append({
                'statement_number': stmt_pos['statement_number'],
                'method': 'line_content_match',
                'confidence': confidence,
                'details': f"Line content '{normalized_line}' found in statement",
                'verification': True,
                'analysis': {
                    'line_content': normalized_line,
                    'exact_match': normalized_line in content
                }
            })

        return candidates

    @staticmethod
    def _deduplicate_candidates(candidates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate candidates and merge information."""
        stmt_candidates = {}

        for candidate in candidates:
            stmt_num = candidate['statement_number']

            if stmt_num not in stmt_candidates:
                stmt_candidates[stmt_num] = candidate
            else:
                # Merge with existing candidate, keeping higher confidence
                existing = stmt_candidates[stmt_num]
                if candidate['confidence'] > existing['confidence']:
                    # Update with better candidate but preserve analysis
                    existing_analysis = existing.get('analysis', {})
                    candidate_analysis = candidate.get('analysis', {})

                    stmt_candidates[stmt_num] = candidate
                    stmt_candidates[stmt_num]['analysis'] = {**existing_analysis, **candidate_analysis}
                    stmt_candidates[stmt_num]['details'] += f"; {existing['details']}"

        # Sort by confidence
        return sorted(stmt_candidates.values(), key=lambda x: x['confidence'], reverse=True)

    @staticmethod
    def _select_best_candidate(candidates: List[Dict[str, Any]], statement_positions: List[Dict[str, Any]],
                             error_info: Dict[str, Any]) -> Dict[str, Any]:
        """Select the best candidate with enhanced verification."""
        best_candidate = candidates[0]

        # Additional verification for the best candidate
        stmt_content = next((s['content'] for s in statement_positions
                           if s['statement_number'] == best_candidate['statement_number']), '')

        # Verify error text presence
        error_text = error_info.get('error_text')
        if error_text and error_text in stmt_content:
            best_candidate['confidence'] = min(1.0, best_candidate['confidence'] + 0.1)
            best_candidate['verification'] = True

        # Verify all quoted texts
        all_quoted_texts = error_info.get('all_quoted_texts', [])
        matched_texts = [text for text in all_quoted_texts if text in stmt_content]
        if matched_texts:
            best_candidate['confidence'] = min(1.0, best_candidate['confidence'] + 0.05 * len(matched_texts))

        return best_candidate

    @staticmethod
    def _fallback_identification(statement_positions: List[Dict[str, Any]],
                               error_info: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback identification when other methods fail."""
        # Use the first statement with highest mapping confidence
        valid_statements = [s for s in statement_positions if s['start_position'] != -1]

        if valid_statements:
            best_stmt = max(valid_statements, key=lambda x: x.get('confidence', 0))
            return {
                'statement_number': best_stmt['statement_number'],
                'confidence': 0.3,
                'method': 'fallback_heuristic',
                'verification': False,
                'candidates': [],
                'reasoning': f"Fallback to statement with best mapping confidence ({best_stmt.get('confidence', 0):.2f})",
                'error_analysis': {'fallback_reason': 'No other methods succeeded'}
            }
        else:
            # Last resort: return first statement
            return {
                'statement_number': 1,
                'confidence': 0.1,
                'method': 'last_resort',
                'verification': False,
                'candidates': [],
                'reasoning': "Last resort: selected first statement",
                'error_analysis': {'fallback_reason': 'No valid position mappings found'}
            }


class UniversalCodeMigrationNodes:
    """
    Universal code migration nodes for database conversion workflows.

    This class provides comprehensive functionality for migrating code between different database systems
    using AI-driven analysis and validation. It supports error identification, source mapping, statement
    conversion, and deployment validation with multi-layer validation approaches.
    """

    def __init__(self, llm):
        """
        Initialize the migration nodes with a language model client.

        Args:
            llm: Language model client for AI-driven analysis and conversion
        """
        self.llm = llm

    def splitStatments(self, state: WorkflowState) -> Dict[str, Any]:
        """
        Split source and target code into individual statements for analysis.

        This method intelligently splits code into individual statements, optimizing for loop iterations
        by reusing source statements when processing updated target code after deployment failures.

        Args:
            state: Current workflow state containing source and target code

        Returns:
            Dict containing split statements and preserved iteration count
        """
        try:
            print("🔍 Starting statement splitting process...")

            # Check if we're in a loop after deployment failure
            is_loop_iteration = hasattr(state, 'updated_target_code') and state.updated_target_code

            # Get source statements - either from previous iteration or by splitting source code
            if is_loop_iteration and hasattr(state, 'source_statements') and state.source_statements:
                print("🔄 Reusing source statements from previous iteration")
                source_statements = state.source_statements
                print(f"🎯 Reusing {len(source_statements)} source statements")
            else:
                # Get the source code from the state
                source_code = state.source_code

                # Split the source code into statements
                print("✂️ Splitting source code into individual statements...")
                source_statements = split_sql_statements(source_code)
                print(f"🎯 Identified {len(source_statements)} source statements")

            # Get target code - either updated or original
            if is_loop_iteration:
                print("🔄 Using updated target code from previous iteration")
                target_code = state.updated_target_code
            else:
                target_code = state.target_code

            # Split the target code into statements
            print("✂️ Splitting target code into individual statements...")
            target_statements = split_sql_statements(target_code)
            print(f"🎯 Identified {len(target_statements)} target statements")

            print(f"📊 Statement Analysis: {len(source_statements)} source statements, {len(target_statements)} target statements")

            # Create dataframes for source and target statements with numbering starting from 1
            source_df = pd.DataFrame({
                "Statement Number": range(1, len(source_statements) + 1),
                "Statement": source_statements
            })

            target_df = pd.DataFrame({
                "Statement Number": range(1, len(target_statements) + 1),
                "Statement": target_statements
            })

            # Get output directory
            output_path = get_output_path()

            # Get filename with iteration number
            excel_filename = get_iteration_filename("statements.xlsx")

            # Save dataframes to Excel file with separate sheets
            excel_path = os.path.join(output_path, excel_filename)
            with pd.ExcelWriter(excel_path) as writer:
                source_df.to_excel(writer, sheet_name="Source Statements", index=False)
                target_df.to_excel(writer, sheet_name="Target Statements", index=False)

            print(f"💾 Saved statements to {os.path.abspath(excel_path)}")
            print(f"📊 Total statements: {len(source_statements)} source, {len(target_statements)} target")
            print(f"✅ Statement splitting completed successfully")

            # Get current iteration count
            iteration_count = getattr(state, 'iteration_count', 1)

            # Log the current iteration count
            print(f"📊 Current iteration count: {iteration_count}")

            # Return the updated state
            return {
                "source_statements": source_statements,
                "target_statements": target_statements,
                "iteration_count": iteration_count  # Preserve iteration count
            }
        except Exception as e:
            print(f"Error in splitStatements node: {str(e)}")
            # Get current iteration count even in case of error
            iteration_count = getattr(state, 'iteration_count', 1)
            return {
                "source_statements": [],
                "target_statements": [],
                "iteration_count": iteration_count  # Preserve iteration count
            }

    def AnalyzeError_identifyTargetStatements(self, state: WorkflowState) -> Dict[str, Any]:
        """
        Analyze deployment error and identify the problematic target statement using two-phase approach.

        Uses a two-phase AI analysis similar to source mapping to accurately identify
        which specific statement is causing the issue, along with surrounding context.

        Phase 1: Identify the primary error statement
        Phase 2: Create error context around the identified statement

        Args:
            state: Current workflow state containing target statements and deployment error

        Returns:
            Dict containing target error context with before/error/after statements
        """
        try:
            print("🔍 Starting two-phase error statement identification...")

            # Get the target statements and deployment error
            target_statements = state.target_statements or []
            deployment_error = state.deployment_error

            # Get feedback from previous validation attempt
            previous_feedback = getattr(state, 'error_identification_feedback', None)

            if previous_feedback:
                print(f"🔄 Previous validation feedback: {previous_feedback}")

            if not target_statements:
                print("⚠️ No target statements to analyze")
                return {}

            if not deployment_error:
                print("⚠️ No deployment error message provided")
                return {}

            print(f"📊 Analyzing {len(target_statements)} target statements with error:\n{deployment_error}")

            # Use two-phase AI approach to identify the error statement
            target_error_context = self.findErrorStatementWithTwoPhaseAI(target_statements, deployment_error, previous_feedback)

            if not target_error_context:
                print("❌ Could not identify error statement")
                return {}

            # Print statement numbers for the identified target statements
            print(f"📊 Identified target statements: Before=#{target_error_context.before_statement_number}, Error=#{target_error_context.error_statement_number}, After=#{target_error_context.after_statement_number}")

            # Save error context to Excel
            self.saveErrorContextToExcel(target_error_context)

            print(f"✅ Error statement identified: Statement #{target_error_context.error_statement_number}")

            return {
                "target_error_context": target_error_context
            }

        except Exception as e:
            print(f"❌ Error in error statement identification: {str(e)}")
            return {}

    def PositionBased_identifyTargetStatements(self, state: WorkflowState) -> Dict[str, Any]:
        """
        Identify the problematic target statement using position-based approach with AI validation.

        This method uses PostgreSQL error position information to precisely identify the error statement,
        then validates the identification using AI. This approach is more accurate than AI-only pattern matching.

        Steps:
        1. Extract position/line information from error message
        2. Map statements to positions in original target code
        3. Use position information to identify error statement
        4. Create error context around identified statement
        5. Return context for validation

        Args:
            state: Current workflow state containing target statements and deployment error

        Returns:
            Dict containing target error context with position-based identification
        """
        try:
            print("🔍 Starting position-based error statement identification...")

            # Get the target statements and deployment error
            target_statements = state.target_statements or []
            deployment_error = state.deployment_error

            # Get target code (original or updated)
            target_code = getattr(state, 'updated_target_code', None) or state.target_code

            # Get feedback from previous validation attempt
            previous_feedback = getattr(state, 'position_identification_feedback', None)

            if previous_feedback:
                print(f"🔄 Previous position identification feedback: {previous_feedback}")

            if not target_statements:
                print("⚠️ No target statements to analyze")
                return {}

            if not deployment_error:
                print("⚠️ No deployment error message provided")
                return {}

            if not target_code:
                print("⚠️ No target code available for position mapping")
                return {}

            print(f"📊 Analyzing {len(target_statements)} target statements with error:\n{deployment_error}")

            # Step 1: Extract error information
            print("🔍 Step 1: Extracting error information...")
            error_info = ErrorInfoExtractor.extract_position_info(deployment_error)

            print(f"📍 Enhanced Error Info:")
            print(f"  Position: {error_info.get('position')}, Line: {error_info.get('line')}")
            print(f"  Error Text: '{error_info.get('error_text')}', Type: {error_info.get('error_type')}")
            print(f"  Category: {error_info.get('error_category')}, All Texts: {error_info.get('all_quoted_texts')}")
            print(f"  Suggested Fix: {error_info.get('suggested_fix')}")

            # Step 2: Map statements to positions with enhanced strategies
            print("🗺️ Step 2: Enhanced mapping statements to positions...")
            statement_positions = StatementPositionMapper.map_statements_to_positions(target_code, target_statements)

            print(f"📊 Mapped {len(statement_positions)} statements to positions")
            for pos in statement_positions[:3]:  # Show first 3 for debugging
                print(f"  Statement #{pos['statement_number']}: {pos['match_method']} "
                      f"(confidence: {pos.get('confidence', 0.0):.2f})")
                print(f"    Position: {pos['start_position']}-{pos['end_position']}, "
                      f"Lines: {pos['start_line']}-{pos['end_line']}")

            # Step 3: Enhanced multi-strategy error identification
            print("🎯 Step 3: Enhanced multi-strategy error identification...")
            identification_result = PositionBasedIdentifier.identify_error_statement(error_info, statement_positions)

            identified_statement_number = identification_result.get('statement_number')
            confidence = identification_result.get('confidence', 0.0)
            method = identification_result.get('method', 'unknown')
            reasoning = identification_result.get('reasoning', 'No reasoning provided')
            verification = identification_result.get('verification', False)
            fallback_used = identification_result.get('fallback_used', False)
            error_analysis = identification_result.get('error_analysis', {})

            # Enhanced logging
            print(f"🎯 Enhanced Identification Results:")
            print(f"  Statement: #{identified_statement_number}")
            print(f"  Method: {method}, Confidence: {confidence:.2f}")
            print(f"  Verification: {verification}, Fallback: {fallback_used}")
            if error_analysis:
                print(f"  Error Analysis: {error_analysis}")

            # Show top candidates for transparency
            candidates = identification_result.get('candidates', [])
            if len(candidates) > 1:
                print(f"  Alternative candidates: {len(candidates)-1}")
                for i, candidate in enumerate(candidates[1:3], 2):  # Show top 2 alternatives
                    print(f"    #{candidate['statement_number']}: {candidate['method']} "
                          f"(confidence: {candidate['confidence']:.2f})")

            if not identified_statement_number:
                print("❌ Could not identify error statement using position-based approach")
                print("🔄 Falling back to AI-based identification...")
                return self.AnalyzeError_identifyTargetStatements(state)

            print(f"🎯 Position-based identification: Statement #{identified_statement_number}")
            print(f"📊 Confidence: {confidence:.2f}, Method: {method}")
            print(f"📝 Reasoning: {reasoning}")

            # Step 4: Create error context around identified statement
            print("🧠 Step 4: Creating error context...")
            target_error_context = self.createErrorContext(target_statements, identified_statement_number)

            if not target_error_context:
                print("❌ Could not create error context")
                return {}

            # Add enhanced identification metadata
            target_error_context.identification_method = method
            target_error_context.identification_confidence = confidence
            target_error_context.identification_reasoning = reasoning
            target_error_context.verification_status = verification
            target_error_context.fallback_used = fallback_used
            target_error_context.error_analysis = error_analysis

            # Print enhanced context summary
            print(f"📊 Enhanced Position-based Context:")
            print(f"  Before: #{target_error_context.before_statement_number}")
            print(f"  Error: #{target_error_context.error_statement_number}")
            print(f"  After: #{target_error_context.after_statement_number}")
            print(f"  Verification: {verification}, Confidence: {confidence:.2f}")

            # Save enhanced error context to Excel with comprehensive information
            self.saveEnhancedPositionBasedErrorContextToExcel(target_error_context, error_info, identification_result)

            print(f"✅ Position-based error statement identified: Statement #{target_error_context.error_statement_number}")

            return {
                "target_error_context": target_error_context,
                "position_identification_info": {
                    "error_info": error_info,
                    "identification_result": identification_result,
                    "statement_positions": statement_positions
                }
            }

        except Exception as e:
            print(f"❌ Error in position-based identification: {str(e)}")
            print("🔄 Falling back to AI-based identification...")
            return self.AnalyzeError_identifyTargetStatements(state)

    def createErrorContext(self, target_statements: List[str], error_statement_number: int) -> ErrorContext:
        """
        Create error context around the identified error statement.

        Args:
            target_statements: List of target statements
            error_statement_number: Number of the identified error statement

        Returns:
            ErrorContext with before/error/after statements
        """
        try:
            if error_statement_number < 1 or error_statement_number > len(target_statements):
                raise ValueError(f"Invalid error statement number: {error_statement_number}")

            # Determine context boundaries
            before_stmt_num = error_statement_number - 1 if error_statement_number > 1 else 0
            error_stmt_num = error_statement_number
            after_stmt_num = error_statement_number + 1 if error_statement_number < len(target_statements) else 0

            # Get statement texts
            before_stmt = target_statements[before_stmt_num - 1] if before_stmt_num > 0 else ""
            error_stmt = target_statements[error_stmt_num - 1] if error_stmt_num > 0 else ""
            after_stmt = target_statements[after_stmt_num - 1] if after_stmt_num > 0 else ""

            return ErrorContext(
                before_statement=before_stmt,
                before_statement_number=before_stmt_num,
                error_statement=error_stmt,
                error_statement_number=error_stmt_num,
                after_statement=after_stmt,
                after_statement_number=after_stmt_num
            )

        except Exception as e:
            print(f"❌ Error creating error context: {str(e)}")
            return None

    def findErrorStatementWithTwoPhaseAI(self, target_statements: List[str], error_message: str, previous_feedback: str = None) -> ErrorContext:
        """
        Use two-phase AI approach to identify the specific statement causing the deployment error.

        Phase 1: Identify the primary error statement with high confidence
        Phase 2: Create error context around the identified statement with enhanced edge case handling

        Args:
            target_statements: List of target code statements to analyze
            error_message: Deployment error message from database system
            previous_feedback: Optional feedback from previous validation attempts

        Returns:
            ErrorContext containing identified error statement and surrounding context
        """
        print(f"🧠 Phase 1: Identifying primary error statement...")

        # Enhanced edge case detection
        total_statements = len(target_statements)
        print(f"📊 Total statements available: {total_statements}")

        if total_statements == 1:
            print("⚠️ Edge case detected: Only 1 statement available - limited context")
        elif total_statements == 2:
            print("⚠️ Edge case detected: Only 2 statements available - minimal context")

        if previous_feedback:
            print(f"📝 Incorporating previous feedback: {previous_feedback}")

        # Phase 1: Identify the primary error statement
        phase1_prompt = create_phase1_error_identification_prompt(target_statements, error_message, previous_feedback)

        # Use structured output for Phase 1
        structured_llm = self.llm.client.with_structured_output(Phase1ErrorIdentificationOutput)
        phase1_result = structured_llm.invoke(phase1_prompt)

        identified_error_statement = phase1_result.error_statement_number
        confidence_score = phase1_result.confidence_score
        reasoning = phase1_result.reasoning

        print(f"🎯 Phase 1 identified statement #{identified_error_statement} as error statement (confidence: {confidence_score:.2f})")
        print(f"📝 Reasoning: {reasoning}")

        if identified_error_statement < 1 or identified_error_statement > len(target_statements):
            raise ValueError(f"Invalid error statement number: {identified_error_statement}")

        # Enhanced edge case logging
        if identified_error_statement == 1 and total_statements > 1:
            print("📍 Edge case: Error at first statement - no before context available")
        elif identified_error_statement == total_statements and total_statements > 1:
            print("📍 Edge case: Error at last statement - no after context available")

        # Phase 2: Create error context around the identified statement
        print(f"🧠 Phase 2: Creating error context around statement #{identified_error_statement}...")

        phase2_prompt = create_phase2_error_context_prompt(target_statements, identified_error_statement, error_message)

        # Use structured output for Phase 2
        structured_llm2 = self.llm.client.with_structured_output(Phase2ErrorContextOutput)
        phase2_result = structured_llm2.invoke(phase2_prompt)

        target_stmts = phase2_result.target_statements
        validation_notes = phase2_result.validation_notes

        print(f"🎯 Phase 2 completed with {len(target_stmts)} context statements")
        if validation_notes:
            print(f"📝 Validation notes: {validation_notes}")

        # Extract the context statements
        before_stmt_num = 0
        error_stmt_num = identified_error_statement
        after_stmt_num = 0

        for stmt in target_stmts:
            if stmt.statement_type == "before_error":
                before_stmt_num = stmt.target_statement_number
            elif stmt.statement_type == "error_statement":
                error_stmt_num = stmt.target_statement_number
            elif stmt.statement_type == "after_error":
                after_stmt_num = stmt.target_statement_number

        # Validate statement numbers
        if error_stmt_num != identified_error_statement:
            raise ValueError(f"Phase 2 error statement mismatch: expected {identified_error_statement}, got {error_stmt_num}")

        # Enhanced edge case validation and logging
        context_count = sum(1 for num in [before_stmt_num, error_stmt_num, after_stmt_num] if num > 0)
        print(f"📊 Context summary: {context_count} statements (Before: {before_stmt_num}, Error: {error_stmt_num}, After: {after_stmt_num})")

        if context_count == 1:
            print("⚠️ Minimal context: Only error statement available")
        elif context_count == 2:
            print("⚠️ Limited context: Only 2 statements available for analysis")

        # Get the actual statement texts with enhanced bounds checking
        before_stmt = target_statements[before_stmt_num-1] if before_stmt_num > 0 and before_stmt_num <= len(target_statements) else ""
        error_stmt = target_statements[error_stmt_num-1] if error_stmt_num > 0 and error_stmt_num <= len(target_statements) else ""
        after_stmt = target_statements[after_stmt_num-1] if after_stmt_num > 0 and after_stmt_num <= len(target_statements) else ""

        # Create and return ErrorContext
        return ErrorContext(
            before_statement=before_stmt,
            before_statement_number=before_stmt_num,
            error_statement=error_stmt,
            error_statement_number=error_stmt_num,
            after_statement=after_stmt,
            after_statement_number=after_stmt_num
        )



    def saveErrorContextToExcel(self, error_context: ErrorContext):
        """Save error context to Excel file."""
        try:
            # Create data for DataFrame with enhanced information
            data = []

            # Before statement
            if error_context.before_statement_number > 0:
                data.append({
                    "Statement Number": error_context.before_statement_number,
                    "Statement": error_context.before_statement,
                    "Type": "Before Error",
                    "Database Specific": "PostgreSQL Target"
                })

            # Error statement
            if error_context.error_statement_number > 0:
                data.append({
                    "Statement Number": error_context.error_statement_number,
                    "Statement": error_context.error_statement,
                    "Type": "ERROR STATEMENT",
                    "Database Specific": "PostgreSQL Target"
                })

            # After statement
            if error_context.after_statement_number > 0:
                data.append({
                    "Statement Number": error_context.after_statement_number,
                    "Statement": error_context.after_statement,
                    "Type": "After Error",
                    "Database Specific": "PostgreSQL Target"
                })

            df = pd.DataFrame(data)

            # Get output directory
            output_path = get_output_path()

            # Get filename with iteration number
            excel_filename = get_iteration_filename("target_error_context.xlsx")

            # Save to Excel
            excel_path = os.path.join(output_path, excel_filename)
            df.to_excel(excel_path, index=False, sheet_name="Error Context")

            print(f"💾 Saved error context to {os.path.abspath(excel_path)}")

        except Exception as e:
            print(f"⚠️ Could not save error context: {e}")

    def savePositionBasedErrorContextToExcel(self, error_context: ErrorContext, error_info: Dict[str, Any], identification_result: Dict[str, Any]):
        """Save position-based error context to Excel file with additional metadata."""
        try:
            # Create data for DataFrame with position-based information
            data = []

            # Before statement
            if error_context.before_statement_number > 0:
                data.append({
                    "Statement Number": error_context.before_statement_number,
                    "Statement": error_context.before_statement,
                    "Type": "Before Error",
                    "Database Specific": "PostgreSQL Target",
                    "Identification Method": "Context",
                    "Position Info": "N/A"
                })

            # Error statement with position information
            if error_context.error_statement_number > 0:
                position_info = f"Position: {error_info.get('position', 'N/A')}, Line: {error_info.get('line', 'N/A')}"
                data.append({
                    "Statement Number": error_context.error_statement_number,
                    "Statement": error_context.error_statement,
                    "Type": "ERROR STATEMENT (Position-Based)",
                    "Database Specific": "PostgreSQL Target",
                    "Identification Method": identification_result.get('method', 'unknown'),
                    "Position Info": position_info,
                    "Confidence": f"{identification_result.get('confidence', 0.0):.2f}",
                    "Reasoning": identification_result.get('reasoning', 'No reasoning'),
                    "Error Text": error_info.get('error_text', 'N/A'),
                    "Error Type": error_info.get('error_type', 'unknown')
                })

            # After statement
            if error_context.after_statement_number > 0:
                data.append({
                    "Statement Number": error_context.after_statement_number,
                    "Statement": error_context.after_statement,
                    "Type": "After Error",
                    "Database Specific": "PostgreSQL Target",
                    "Identification Method": "Context",
                    "Position Info": "N/A"
                })

            df = pd.DataFrame(data)

            # Get output directory
            output_path = get_output_path()

            # Get filename with iteration number (shortened)
            excel_filename = get_iteration_filename("pos_error_context.xlsx")

            # Save to Excel
            excel_path = os.path.join(output_path, excel_filename)
            df.to_excel(excel_path, index=False, sheet_name="Position Error Context")

            print(f"💾 Saved position-based error context to {os.path.abspath(excel_path)}")

        except Exception as e:
            print(f"⚠️ Could not save position-based error context: {e}")

    def saveEnhancedPositionBasedErrorContextToExcel(self, error_context: ErrorContext, error_info: Dict[str, Any], identification_result: Dict[str, Any]):
        """Save enhanced position-based error context to Excel file with comprehensive metadata."""
        try:
            # Create data for DataFrame with enhanced position-based information
            data = []

            # Before statement
            if error_context.before_statement_number > 0:
                data.append({
                    "Statement Number": error_context.before_statement_number,
                    "Statement": error_context.before_statement,
                    "Type": "Before Error",
                    "Database Specific": "PostgreSQL Target",
                    "Identification Method": "Context",
                    "Position Info": "N/A",
                    "Confidence": "N/A",
                    "Reasoning": "Context statement",
                    "Error Text": "N/A",
                    "Error Type": "N/A",
                    "Error Category": "N/A",
                    "Verification": "N/A",
                    "Fallback Used": "N/A"
                })

            # Error statement with comprehensive information
            if error_context.error_statement_number > 0:
                position_info = f"Position: {error_info.get('position', 'N/A')}, Line: {error_info.get('line', 'N/A')}"

                data.append({
                    "Statement Number": error_context.error_statement_number,
                    "Statement": error_context.error_statement,
                    "Type": "ERROR STATEMENT (Enhanced Position-Based)",
                    "Database Specific": "PostgreSQL Target",
                    "Identification Method": identification_result.get('method', 'unknown'),
                    "Position Info": position_info,
                    "Confidence": f"{identification_result.get('confidence', 0.0):.3f}",
                    "Reasoning": identification_result.get('reasoning', 'No reasoning'),
                    "Error Text": error_info.get('error_text', 'N/A'),
                    "Error Type": error_info.get('error_type', 'unknown'),
                    "Error Category": error_info.get('error_category', 'unknown'),
                    "Verification": str(identification_result.get('verification', False)),
                    "Fallback Used": str(identification_result.get('fallback_used', False)),
                    "All Quoted Texts": str(error_info.get('all_quoted_texts', [])),
                    "Suggested Fix": error_info.get('suggested_fix', 'N/A'),
                    "Error Analysis": str(identification_result.get('error_analysis', {})),
                    "Candidates Count": len(identification_result.get('candidates', []))
                })

            # After statement
            if error_context.after_statement_number > 0:
                data.append({
                    "Statement Number": error_context.after_statement_number,
                    "Statement": error_context.after_statement,
                    "Type": "After Error",
                    "Database Specific": "PostgreSQL Target",
                    "Identification Method": "Context",
                    "Position Info": "N/A",
                    "Confidence": "N/A",
                    "Reasoning": "Context statement",
                    "Error Text": "N/A",
                    "Error Type": "N/A",
                    "Error Category": "N/A",
                    "Verification": "N/A",
                    "Fallback Used": "N/A"
                })

            df = pd.DataFrame(data)

            # Get output directory
            output_path = get_output_path()

            # Get filename with iteration number (enhanced)
            excel_filename = get_iteration_filename("enhanced_pos_error_context.xlsx")

            # Save to Excel with enhanced formatting
            excel_path = os.path.join(output_path, excel_filename)
            with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name="Enhanced Error Context")

                # Get the workbook and worksheet
                workbook = writer.book
                worksheet = writer.sheets["Enhanced Error Context"]

                # Auto-adjust column widths
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            print(f"💾 Saved enhanced position-based error context to {os.path.abspath(excel_path)}")

        except Exception as e:
            print(f"⚠️ Could not save enhanced position-based error context: {e}")

    def validate_error_identification(self, state: WorkflowState) -> Dict[str, Any]:
        """Validate the error identification using AI."""
        try:
            print("🔍 Starting error identification validation...")

            # Get the target error context
            target_error_context = state.target_error_context
            deployment_error = state.deployment_error

            # Get current validation attempts count
            validation_attempts = getattr(state, 'validation_attempts', 0) + 1
            print(f"📊 Error identification validation attempt {validation_attempts}")

            if not target_error_context:
                print("⚠️ No target error context to validate")
                return {
                    "validation_successful": False,
                    "validation_attempts": validation_attempts
                }

            print(f"📊 Validating error identification for statement #{target_error_context.error_statement_number}")

            # Use AI to validate the error identification using just the context statements
            validation_result = self.validateErrorIdentificationWithContext(
                target_error_context,
                deployment_error
            )

            # Extract validation success and explanation
            validation_successful = validation_result.get('is_correct', False)
            explanation = validation_result.get('explanation', 'No explanation provided')
            confidence = validation_result.get('confidence', 0.0)

            # Save validation results to Excel
            self.saveErrorValidationResultsToExcel(
                target_error_context,
                deployment_error,
                validation_successful,
                explanation
            )

            if validation_successful:
                print(f"✅ Error identification validated successfully with confidence {confidence}")
                return {
                    "validation_successful": True,
                    "validation_attempts": validation_attempts,
                    "error_identification_feedback": None  # Clear feedback on success
                }
            else:
                print(f"❌ Error identification validation failed with confidence {confidence}")
                print(f"📝 Validation feedback: {explanation}")

                # Return the result along with the updated attempts count and feedback
                return {
                    "validation_successful": False,
                    "validation_attempts": validation_attempts,
                    "error_identification_feedback": explanation  # Store failure feedback
                }

        except Exception as e:
            print(f"❌ Error in validation: {str(e)}")
            # Get current validation attempts count even in case of error
            validation_attempts = getattr(state, 'validation_attempts', 0) + 1
            return {
                "validation_successful": False,
                "validation_attempts": validation_attempts,
                "error_identification_feedback": f"Validation error: {str(e)}"  # Store error as feedback
            }

    def validate_position_identification(self, state: WorkflowState) -> Dict[str, Any]:
        """Validate the position-based error identification using AI."""
        try:
            print("🔍 Starting position-based error identification validation...")

            # Get the target error context and position information
            target_error_context = state.target_error_context
            deployment_error = state.deployment_error
            position_info = getattr(state, 'position_identification_info', {})

            # Get current validation attempts count
            validation_attempts = getattr(state, 'validation_attempts', 0) + 1
            print(f"📊 Position-based validation attempt {validation_attempts}")

            if not target_error_context:
                print("⚠️ No target error context to validate")
                return {
                    "validation_successful": False,
                    "validation_attempts": validation_attempts
                }

            print(f"📊 Validating position-based identification for statement #{target_error_context.error_statement_number}")

            # Use AI to validate the position-based identification
            validation_result = self.validatePositionBasedIdentification(
                target_error_context,
                deployment_error,
                position_info
            )

            # Extract validation success and explanation
            validation_successful = validation_result.get('is_correct', False)
            explanation = validation_result.get('explanation', 'No explanation provided')
            confidence = validation_result.get('confidence', 0.0)

            # Save validation results to Excel
            self.savePositionValidationResultsToExcel(
                target_error_context,
                deployment_error,
                position_info,
                validation_successful,
                explanation
            )

            if validation_successful:
                print(f"✅ Position-based identification validated successfully with confidence {confidence}")
                return {
                    "validation_successful": True,
                    "validation_attempts": validation_attempts,
                    "position_identification_feedback": None  # Clear feedback on success
                }
            else:
                print(f"❌ Position-based identification validation failed with confidence {confidence}")
                print(f"📝 Validation feedback: {explanation}")

                # Return the result along with the updated attempts count and feedback
                return {
                    "validation_successful": False,
                    "validation_attempts": validation_attempts,
                    "position_identification_feedback": explanation  # Store failure feedback
                }

        except Exception as e:
            print(f"❌ Error in position-based validation: {str(e)}")
            # Get current validation attempts count even in case of error
            validation_attempts = getattr(state, 'validation_attempts', 0) + 1
            return {
                "validation_successful": False,
                "validation_attempts": validation_attempts,
                "position_identification_feedback": f"Position validation error: {str(e)}"  # Store error as feedback
            }

    def validatePositionBasedIdentification(self, target_error_context: ErrorContext, error_message: str, position_info: Dict[str, Any]) -> Dict[str, Any]:
        """Use AI to validate if the position-based identified error statement is correct."""
        print(f"🧠 Using AI to validate position-based identification...")

        # Extract position information
        error_info = position_info.get('error_info', {})
        identification_result = position_info.get('identification_result', {})

        # Create validation prompt for position-based identification
        prompt = self.createPositionValidationPrompt(target_error_context, error_message, error_info, identification_result)

        # Use structured output for reliable parsing
        structured_llm = self.llm.client.with_structured_output(ValidationOutput)

        try:
            validation_result = structured_llm.invoke(prompt)

            result = {
                'is_correct': validation_result.is_correct,
                'confidence': validation_result.confidence,
                'explanation': validation_result.explanation
            }

            print(f"🎯 AI validation result: correct={result['is_correct']}, confidence={result['confidence']}")
            print(f"📝 Explanation: {result['explanation']}")

            return result

        except Exception as e:
            print(f"❌ Error in AI validation: {str(e)}")
            return {
                'is_correct': False,
                'confidence': 0.0,
                'explanation': f"Validation failed due to error: {str(e)}"
            }

    def createPositionValidationPrompt(self, target_error_context: ErrorContext, error_message: str, error_info: Dict[str, Any], identification_result: Dict[str, Any]) -> str:
        """Create a validation prompt for position-based identification."""

        position = error_info.get('position', 'N/A')
        line = error_info.get('line', 'N/A')
        error_text = error_info.get('error_text', 'N/A')
        method = identification_result.get('method', 'unknown')
        reasoning = identification_result.get('reasoning', 'No reasoning')

        return f"""You are an Oracle to PostgreSQL Database Migration Expert. Your task is to validate if the position-based error statement identification is correct.

ERROR MESSAGE:
{error_message}

POSITION INFORMATION:
- Position: {position}
- Line: {line}
- Error Text: "{error_text}"
- Identification Method: {method}
- Reasoning: {reasoning}

IDENTIFIED ERROR STATEMENT:
Statement #{target_error_context.error_statement_number}:
{target_error_context.error_statement}

CONTEXT:
Before Statement #{target_error_context.before_statement_number}:
{target_error_context.before_statement}

After Statement #{target_error_context.after_statement_number}:
{target_error_context.after_statement}

VALIDATION TASK:
1. Verify if the identified statement contains the problematic syntax mentioned in the error
2. Check if the error text fragment actually exists in the identified statement
3. Confirm that this statement would cause the specific PostgreSQL error
4. Assess if the position-based identification is accurate

VALIDATION CRITERIA:
- Does the statement contain the exact error text from the message?
- Would this statement cause the reported PostgreSQL error?
- Is the position mapping logical and accurate?
- Are there any obvious misidentifications?

Provide your validation result with confidence score (0.0-1.0) and detailed explanation."""

    def savePositionValidationResultsToExcel(self, target_error_context: ErrorContext, error_message: str, position_info: Dict[str, Any], is_correct: bool, explanation: str):
        """Save position-based validation results to Excel file."""
        try:
            error_info = position_info.get('error_info', {})
            identification_result = position_info.get('identification_result', {})

            # Create data for DataFrame
            data = [
                {
                    "Validation Result": "PASSED" if is_correct else "FAILED",
                    "Explanation": explanation,
                    "Error Message": error_message,
                    "Position": error_info.get('position', 'N/A'),
                    "Line": error_info.get('line', 'N/A'),
                    "Error Text": error_info.get('error_text', 'N/A'),
                    "Error Type": error_info.get('error_type', 'unknown'),
                    "Identification Method": identification_result.get('method', 'unknown'),
                    "Identification Confidence": f"{identification_result.get('confidence', 0.0):.2f}",
                    "Identification Reasoning": identification_result.get('reasoning', 'No reasoning'),
                    "Target Error Statement Number": target_error_context.error_statement_number,
                    "Target Error Statement": target_error_context.error_statement,
                    "Timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            ]

            df = pd.DataFrame(data)

            # Get output directory
            output_path = get_output_path()

            # Get filename with iteration number (shortened)
            excel_filename = get_iteration_filename("pos_validation.xlsx")

            # Save to Excel
            excel_path = os.path.join(output_path, excel_filename)
            df.to_excel(excel_path, index=False, sheet_name="Position Validation")

            print(f"💾 Saved position-based validation results to {os.path.abspath(excel_path)}")

        except Exception as e:
            print(f"⚠️ Could not save position-based validation results: {e}")

    def saveErrorValidationResultsToExcel(self, target_error_context: ErrorContext, error_message: str, is_correct: bool, explanation: str):
        """Save error validation results to Excel file."""
        try:
            # Create data for DataFrame
            data = [
                {
                    "Validation Result": "PASSED" if is_correct else "FAILED",
                    "Explanation": explanation,
                    "Error Message": error_message,
                    "Target Before Statement Number": target_error_context.before_statement_number,
                    "Target Before Statement": target_error_context.before_statement,
                    "Target Error Statement Number": target_error_context.error_statement_number,
                    "Target Error Statement": target_error_context.error_statement,
                    "Target After Statement Number": target_error_context.after_statement_number,
                    "Target After Statement": target_error_context.after_statement,
                    "Timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            ]

            df = pd.DataFrame(data)

            # Get output directory
            output_path = get_output_path()

            # Get filename with iteration number (shortened)
            excel_filename = get_iteration_filename("error_validation.xlsx")

            # Save to Excel
            excel_path = os.path.join(output_path, excel_filename)
            df.to_excel(excel_path, index=False, sheet_name="Error Validation")

            print(f"💾 Saved error validation results to {os.path.abspath(excel_path)}")

        except Exception as e:
            print(f"⚠️ Could not save error validation results: {e}")

    def validateErrorIdentificationWithContext(self, target_error_context: ErrorContext, error_message: str) -> Dict[str, Any]:
        """Use AI to validate if the identified error statement is correct using the error context with edge case handling."""
        print(f"🧠 Using AI to validate error identification...")

        # Detect edge cases based on available context
        available_count = sum(1 for num in [target_error_context.before_statement_number,
                                          target_error_context.error_statement_number,
                                          target_error_context.after_statement_number] if num > 0)

        is_edge_case = available_count <= 2

        if is_edge_case:
            print(f"⚠️ Edge case detected: Only {available_count} statement(s) available for validation")

            # Determine edge case type
            context_type = "single" if available_count == 1 else "minimal"
            if target_error_context.error_statement_number == 1:
                context_type = "first_statement"
            elif target_error_context.after_statement_number == 0 and target_error_context.before_statement_number > 0:
                context_type = "last_statement"

            # Use edge case enhanced prompt with error message
            prompt = create_edge_case_validation_prompt(target_error_context, error_message, context_type)
            print(f"📍 Using edge case validation strategy: {context_type}")
        else:
            # Use standard validation prompt with error message
            prompt = create_error_validation_prompt(target_error_context, error_message)
            print("📍 Using standard validation strategy")

        # Use structured output for reliable parsing
        structured_llm = self.llm.client.with_structured_output(ValidationOutput)
        ai_result = structured_llm.invoke(prompt)

        is_correct = ai_result.is_correct
        confidence = ai_result.confidence
        explanation = ai_result.explanation

        # Create result
        result = {
            'is_correct': is_correct,
            'explanation': explanation,
            'confidence': confidence,
            'edge_case_handled': is_edge_case,
            'context_type': context_type if is_edge_case else "standard"
        }

        print(f"🎯 AI validation result: correct={is_correct}, confidence={confidence}")
        if is_edge_case:
            print(f"📍 Edge case handling: {context_type}")
        print(f"📝 Explanation: {explanation}")

        return result

    def mapSource_withTargetStatements(self, state: WorkflowState) -> Dict[str, Any]:
        """Map target error context to corresponding source statements."""
        try:
            print("🔍 Starting source statement mapping...")

            # Get the source statements and target error context
            source_statements = state.source_statements or []
            target_error_context = state.target_error_context

            # Get feedback from previous validation attempt
            previous_feedback = getattr(state, 'source_mapping_feedback', None)

            if previous_feedback:
                print(f"🔄 Previous mapping feedback: {previous_feedback}")

            if not source_statements:
                print("⚠️ No source statements to analyze")
                return {}

            if not target_error_context:
                print("⚠️ No target error context provided")
                return {}

            print(f"📊 Finding corresponding source statements for target error context")

            # Extract target context statements
            target_context = [
                (target_error_context.before_statement_number, target_error_context.before_statement),
                (target_error_context.error_statement_number, target_error_context.error_statement),
                (target_error_context.after_statement_number, target_error_context.after_statement)
            ]

            # Filter out invalid statements
            target_context = [(num, stmt) for num, stmt in target_context if num > 0 and stmt]
            print(f"📊 Found {len(target_context)} valid target context statements for mapping")

            # Enhanced edge case detection for source mapping
            if len(target_context) == 1:
                print("⚠️ Edge case: Only error statement available for source mapping - limited context")
            elif len(target_context) == 2:
                print("⚠️ Edge case: Only 2 statements available for source mapping - minimal context")

            # Log which statements are missing for better debugging
            missing_context = []
            if target_error_context.before_statement_number == 0:
                missing_context.append("before")
            if target_error_context.after_statement_number == 0:
                missing_context.append("after")
            if missing_context:
                print(f"📍 Missing context: {', '.join(missing_context)} statement(s) not available")
            # Use AI to find corresponding source statements with feedback
            source_context = self.findSourceStatementsWithAI(source_statements, target_context, target_error_context, previous_feedback)

            if not source_context:
                print("❌ Could not identify corresponding source statements")
                return {}

            # Print statement numbers for the identified source statements
            print(f"📊 Identified source statements: Before=#{source_context.before_statement_number}, Error=#{source_context.error_statement_number}, After=#{source_context.after_statement_number}")

            # Save source context to Excel
            self.saveSourceContextToExcel(source_context)

            print(f"✅ Source statements identified")

            return {
                "source_context": source_context
            }

        except Exception as e:
            print(f"❌ Error in source statement mapping: {str(e)}")
            return {}

    def findSourceStatementsWithAI(self, source_statements: List[str], target_context: List[Tuple[int, str]], target_error_context: ErrorContext, previous_feedback: str = None) -> ErrorContext:
        """Use AI to find the corresponding source statements for the target error context using two-phase approach with enhanced edge case handling."""
        # Extract target error statement for Phase 1
        target_error_statement = ""
        target_error_number = 0

        print(f"🔍 Analyzing target context with {len(target_context)} statements for source mapping")

        # Use the correct error statement number from target_error_context
        # This is the authoritative source of which statement is the error statement
        actual_error_statement_number = target_error_context.error_statement_number
        print(f"🎯 Using authoritative error statement number: #{actual_error_statement_number}")

        # Find the error statement in the target_context using the correct statement number
        target_error_statement = ""
        target_error_number = actual_error_statement_number

        for stmt_num, stmt_text in target_context:
            if stmt_num == actual_error_statement_number:
                target_error_statement = stmt_text
                target_error_number = stmt_num
                print(f"✅ Found error statement #{stmt_num} in target context")
                break

        if not target_error_statement:
            print(f"❌ Error statement #{actual_error_statement_number} not found in target context")
            print(f"📊 Available target context statements: {[num for num, _ in target_context]}")
            return None

        # Log the context type for debugging
        if len(target_context) == 1:
            print(f"📍 Single statement context: Statement #{target_error_number} (error statement)")
        elif len(target_context) == 2:
            print(f"📍 Two statement context: Statement #{target_error_number} (error statement) + 1 context statement")
        elif len(target_context) == 3:
            print(f"📍 Three statement context: Statement #{target_error_number} (error statement) + before/after context")
        else:
            print(f"📍 {len(target_context)} statement context: Statement #{target_error_number} (error statement)")

        print(f"🧠 Phase 1: Identifying error statement mapping...")

        if previous_feedback:
            print(f"📝 Incorporating previous mapping feedback: {previous_feedback}")

        # Phase 1: Identify the error statement mapping with feedback
        phase1_prompt = create_error_statement_identification_prompt(
            source_statements, target_error_statement, target_error_number, previous_feedback
        )

        # Use structured output for Phase 1
        structured_llm = self.llm.client.with_structured_output(Phase1IdentificationOutput)
        phase1_result = structured_llm.invoke(phase1_prompt)

        identified_error_source = phase1_result.source_statement_number
        confidence_score = phase1_result.confidence_score
        reasoning = phase1_result.reasoning

        print(f"🎯 Phase 1 identified source statement #{identified_error_source} (confidence: {confidence_score:.2f})")
        print(f"📝 Reasoning: {reasoning}")

        # Check if this is a PostgreSQL-specific statement (should map to 0)
        if identified_error_source == 0:
            print(f"🎯 PostgreSQL-specific statement detected - no Oracle equivalent")
            print(f"🎯 Creating special mapping for PostgreSQL-specific error statement")
        elif identified_error_source < 1 or identified_error_source > len(source_statements):
            raise ValueError(f"Invalid source statement number: {identified_error_source}")

        # Phase 2: Create sequential mapping based on identified error statement
        print(f"🧠 Phase 2: Creating sequential mapping around source #{identified_error_source}...")
        print(f"🎯 Using actual error target number: #{actual_error_statement_number}")

        phase2_prompt = create_sequential_mapping_prompt(
            source_statements, target_context, identified_error_source, actual_error_statement_number
        )

        # Use structured output for Phase 2
        structured_llm2 = self.llm.client.with_structured_output(Phase2MappingOutput)
        phase2_result = structured_llm2.invoke(phase2_prompt)

        source_stmts = phase2_result.source_statements
        validation_notes = phase2_result.validation_notes

        print(f"🎯 Phase 2 completed with {len(source_stmts)} mappings")
        if validation_notes:
            print(f"📝 Validation notes: {validation_notes}")

        if not source_stmts:
            raise ValueError("Phase 2 did not return any source statements")

        # Enhanced logging for Phase 2 results
        print(f"📊 Phase 2 detailed mappings:")
        for i, stmt in enumerate(source_stmts):
            print(f"  {i+1}. Target #{stmt.target_statement_number} → Source #{stmt.source_statement_number} ({stmt.statement_type})")
            if stmt.source_statement_number == 0:
                print(f"     ⚠️ PostgreSQL-specific statement (no Oracle equivalent)")

        # Process the source statements from Phase 2
        before_stmt_num = 0
        error_stmt_num = 0
        after_stmt_num = 0

        for stmt in source_stmts:
            stmt_type = stmt.statement_type
            source_num = stmt.source_statement_number

            if stmt_type == 'before_error':
                before_stmt_num = source_num
            elif stmt_type == 'error_statement':
                error_stmt_num = source_num
            elif stmt_type == 'after_error':
                after_stmt_num = source_num

        print(f"🎯 AI identified source statements: before={before_stmt_num}, error={error_stmt_num}, after={after_stmt_num}")

        # Handle PostgreSQL-specific case where error statement maps to 0
        if error_stmt_num == 0:
            print(f"🎯 Error statement is target database-specific - creating minimal source context")

        # Validate that the statement numbers are different and valid
        # Allow error_stmt_num to be 0 for PostgreSQL-specific statements
        if (before_stmt_num == 0 and error_stmt_num == 0 and after_stmt_num == 0):
            raise ValueError("AI returned no valid statement numbers")

        # Check for duplicate statement numbers (only if they are non-zero)
        non_zero_nums = [num for num in [before_stmt_num, error_stmt_num, after_stmt_num] if num > 0]
        if len(non_zero_nums) != len(set(non_zero_nums)):
            raise ValueError("AI returned duplicate statement numbers")

        # Create source context
        source_context = ErrorContext(
            error_statement=source_statements[error_stmt_num-1] if 1 <= error_stmt_num <= len(source_statements) else "",
            error_statement_number=error_stmt_num,
            before_statement=source_statements[before_stmt_num-1] if 1 <= before_stmt_num <= len(source_statements) else "",
            before_statement_number=before_stmt_num,
            after_statement=source_statements[after_stmt_num-1] if 1 <= after_stmt_num <= len(source_statements) else "",
            after_statement_number=after_stmt_num
        )

        return source_context

    def saveSourceContextToExcel(self, source_context: ErrorContext):
        """Save source context to Excel file."""
        try:
            # Create data for DataFrame with PostgreSQL-specific indicators
            data = []

            # Before statement
            if source_context.before_statement_number > 0 or source_context.before_statement.strip():
                before_type = "Before Error"
                if source_context.before_statement_number == 0:
                    before_type = "Before Error (PostgreSQL-Specific)"
                data.append({
                    "Statement Number": source_context.before_statement_number,
                    "Statement": source_context.before_statement,
                    "Type": before_type,
                    "Database Specific": "PostgreSQL-Specific" if source_context.before_statement_number == 0 else "Oracle Source"
                })

            # Error statement
            if source_context.error_statement_number > 0 or source_context.error_statement.strip():
                error_type = "ERROR STATEMENT"
                if source_context.error_statement_number == 0:
                    error_type = "ERROR STATEMENT (PostgreSQL-Specific)"
                data.append({
                    "Statement Number": source_context.error_statement_number,
                    "Statement": source_context.error_statement,
                    "Type": error_type,
                    "Database Specific": "PostgreSQL-Specific" if source_context.error_statement_number == 0 else "Oracle Source"
                })

            # After statement
            if source_context.after_statement_number > 0 or source_context.after_statement.strip():
                after_type = "After Error"
                if source_context.after_statement_number == 0:
                    after_type = "After Error (PostgreSQL-Specific)"
                data.append({
                    "Statement Number": source_context.after_statement_number,
                    "Statement": source_context.after_statement,
                    "Type": after_type,
                    "Database Specific": "PostgreSQL-Specific" if source_context.after_statement_number == 0 else "Oracle Source"
                })

            df = pd.DataFrame(data)

            # Get output directory
            output_path = get_output_path()

            # Get filename with iteration number
            excel_filename = get_iteration_filename("source_mappings.xlsx")

            # Save to Excel
            excel_path = os.path.join(output_path, excel_filename)
            df.to_excel(excel_path, index=False, sheet_name="Source Context")

            print(f"💾 Saved source context to {os.path.abspath(excel_path)}")

        except Exception as e:
            print(f"⚠️ Could not save source context: {e}")

    def validate_source_mapping(self, state: WorkflowState) -> Dict[str, Any]:
        """Validate the source mapping using AI."""
        try:
            print("🔍 Starting source mapping validation...")

            # Get the source context
            source_context = state.source_context
            target_error_context = state.target_error_context

            # Get current source mapping attempts count
            source_mapping_attempts = getattr(state, 'source_mapping_attempts', 0) + 1
            print(f"📊 Source mapping validation attempt {source_mapping_attempts}")



            if not source_context:
                print("⚠️ No source context to validate")
                return {
                    "source_mapping_successful": False,
                    "source_mapping_attempts": source_mapping_attempts
                }

            if not target_error_context:
                print("⚠️ No target error context to validate against")
                return {
                    "source_mapping_successful": False,
                    "source_mapping_attempts": source_mapping_attempts
                }

            print(f"📊 Validating source mapping for error statement #{target_error_context.error_statement_number}")

            # Use AI to validate the source mapping
            validation_result = self.validateSourceMappingWithAI(
                source_context,
                target_error_context
            )

            # Extract validation success and explanation
            source_mapping_successful = validation_result.get('is_correct', False)
            explanation = validation_result.get('explanation', 'No explanation provided')

            # Save validation results to Excel
            self.saveValidationResultsToExcel(
                source_context,
                target_error_context,
                source_mapping_successful,
                explanation
            )

            if source_mapping_successful:
                print(f"✅ Source mapping validated successfully")
                return {
                    "source_mapping_successful": True,
                    "source_mapping_attempts": source_mapping_attempts,
                    "source_mapping_feedback": None  # Clear feedback on success
                }
            else:
                print(f"❌ Source mapping validation failed")
                print(f"📝 Mapping feedback: {explanation}")

                # Return the result along with the updated attempts count and feedback
                return {
                    "source_mapping_successful": False,
                    "source_mapping_attempts": source_mapping_attempts,
                    "source_mapping_feedback": explanation  # Store failure feedback
                }

        except Exception as e:
            print(f"❌ Error in source mapping validation: {str(e)}")
            # Get current source mapping attempts count even in case of error
            source_mapping_attempts = getattr(state, 'source_mapping_attempts', 0) + 1
            return {
                "source_mapping_successful": False,
                "source_mapping_attempts": source_mapping_attempts,
                "source_mapping_feedback": f"Validation error: {str(e)}"  # Store error as feedback
            }

    def saveValidationResultsToExcel(self, source_context: ErrorContext, target_error_context: ErrorContext, is_correct: bool, explanation: str):
        """Save validation results to Excel file."""
        try:
            # Create data for DataFrame with PostgreSQL-specific indicators
            data = [
                {
                    "Validation Result": "PASSED" if is_correct else "FAILED",
                    "Explanation": explanation,
                    "Target Before Statement Number": target_error_context.before_statement_number,
                    "Target Before Statement": target_error_context.before_statement,
                    "Target Error Statement Number": target_error_context.error_statement_number,
                    "Target Error Statement": target_error_context.error_statement,
                    "Target After Statement Number": target_error_context.after_statement_number,
                    "Target After Statement": target_error_context.after_statement,
                    "Source Before Statement Number": source_context.before_statement_number,
                    "Source Before Statement": source_context.before_statement,
                    "Source Before Type": "PostgreSQL-Specific" if source_context.before_statement_number == 0 else "Oracle Source",
                    "Source Error Statement Number": source_context.error_statement_number,
                    "Source Error Statement": source_context.error_statement,
                    "Source Error Type": "PostgreSQL-Specific" if source_context.error_statement_number == 0 else "Oracle Source",
                    "Source After Statement Number": source_context.after_statement_number,
                    "Source After Statement": source_context.after_statement,
                    "Source After Type": "PostgreSQL-Specific" if source_context.after_statement_number == 0 else "Oracle Source",
                    "Timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            ]

            df = pd.DataFrame(data)

            # Get output directory
            output_path = get_output_path()

            # Get filename with iteration number (shortened)
            excel_filename = get_iteration_filename("src_tgt_validation.xlsx")

            # Save to Excel
            excel_path = os.path.join(output_path, excel_filename)
            df.to_excel(excel_path, index=False, sheet_name="Validation Results")

            print(f"💾 Saved validation results to {os.path.abspath(excel_path)}")

        except Exception as e:
            print(f"⚠️ Could not save validation results: {e}")

    def validateSourceMappingWithAI(self, source_context: ErrorContext, target_error_context: ErrorContext) -> Dict[str, Any]:
        """Use AI to validate if the source mapping is correct with enhanced multi-layer validation."""
        print(f"🧠 Using enhanced AI validation...")

        # Use single comprehensive AI validation
        prompt = create_source_mapping_validation_prompt(source_context, target_error_context)

        # Use structured output for reliable parsing
        structured_llm = self.llm.client.with_structured_output(ValidationOutput)
        ai_result = structured_llm.invoke(prompt)

        result = {
            'is_correct': ai_result.is_correct,
            'explanation': ai_result.explanation,
            'confidence': ai_result.confidence
        }

        if result['is_correct']:
            print(f"✅ Enhanced validation passed")
        else:
            print(f"⚠️ Enhanced validation failed: {result.get('explanation', 'Unknown reason')}")

        return result



    def Convert_TargetStatement(self, state: WorkflowState) -> Dict[str, Any]:
        """Convert the error statement from Oracle to PostgreSQL format."""
        try:
            print("🔄 Starting statement conversion...")

            # Get the source and target contexts
            source_context = state.source_context
            target_error_context = state.target_error_context
            target_statements = state.target_statements or []
            deployment_error = state.deployment_error

            # Get feedback from previous validation attempt
            previous_feedback = getattr(state, 'conversion_feedback', None)

            if previous_feedback:
                print(f"🔄 Previous conversion feedback: {previous_feedback}")

            # Check if this is a target-specific optimization scenario
            is_target_specific_optimization = (source_context and
                                             source_context.error_statement_number == 0)

            if is_target_specific_optimization:
                print("🚀 OPTIMIZATION MODE: Target-specific statement conversion")
                print("💡 Applying PostgreSQL expertise directly (no source reference needed)")

            if not source_context:
                print("⚠️ No source context provided")
                return {}

            if not target_error_context:
                print("⚠️ No target error context provided")
                return {}

            if not deployment_error:
                print("⚠️ No deployment error provided")
                deployment_error = "ERROR: operator does not exist: integer = character varying\nHINT: No operator matches the given name and argument types. You might need to add explicit type casts."
                print(f"⚠️ Using default error message: {deployment_error}")

            print(f"📊 Converting error statement")

            # For conversion, preserve position information to help target exact error location
            # Don't clean the error message for conversion - AI needs position info
            conversion_result = self.convertStatementWithAI(source_context, target_error_context, target_statements, deployment_error, previous_feedback)

            if not conversion_result or not conversion_result.get("ai_corrections"):
                print("❌ Could not convert error statement")
                return {}

            # Extract AI corrections and original statements
            ai_corrections = conversion_result["ai_corrections"]
            explanation = conversion_result["explanation"]
            original_target_statements = conversion_result["original_target_statements"]

            print(f"✅ Error statement conversion completed - {len(ai_corrections)} corrections generated")
            print(f"📝 AI Explanation: {explanation}")

            return {
                "ai_corrections": ai_corrections,
                "conversion_explanation": explanation,
                "original_target_statements": original_target_statements,
                "target_specific_optimization": is_target_specific_optimization  # Track optimization usage
            }

        except Exception as e:
            print(f"❌ Error in statement conversion: {str(e)}")
            return {}

    def convertStatementWithAI(self, source_context: ErrorContext, target_error_context: ErrorContext, target_statements: List[str], error_message: str, previous_feedback: str = None) -> Dict[str, Any]:
        """Use AI to convert the error statement from Oracle to PostgreSQL format using structured output."""
        print(f"🧠 Using AI to convert error statement...")

        if previous_feedback:
            print(f"📝 Incorporating previous conversion feedback: {previous_feedback}")

        # Create prompt for AI using the imported function with feedback
        prompt = create_statement_conversion_prompt(source_context, target_error_context, error_message, target_statements, previous_feedback)

        # Use structured output for reliable parsing
        structured_llm = self.llm.client.with_structured_output(StatementConversionOutput)
        ai_result = structured_llm.invoke(prompt)

        corrected_stmts = ai_result.corrected_statements
        explanation = ai_result.explanation

        print(f"🎯 AI provided {len(corrected_stmts)} corrected statements")
        print(f"📝 Explanation: {explanation}")

        if not corrected_stmts:
            raise ValueError("AI did not return any corrected statements")

        # Return the AI corrections without applying them to target statements
        # The actual replacement will happen in targetcode_deployment node
        print(f"📋 Returning {len(corrected_stmts)} AI-generated corrections for validation")

        return {
            "ai_corrections": corrected_stmts,
            "explanation": explanation,
            "original_target_statements": target_statements
        }

    def saveCorrectedStatementsToExcel(self, corrected_statements: List[str], target_error_context: ErrorContext):
        """Save corrected statements to Excel file."""
        try:
            # Create data for DataFrame
            data = [
                {"Statement Number": target_error_context.before_statement_number,
                 "Statement": corrected_statements[target_error_context.before_statement_number-1] if 1 <= target_error_context.before_statement_number <= len(corrected_statements) else "",
                 "Type": "Before Error"},
                {"Statement Number": target_error_context.error_statement_number,
                 "Statement": corrected_statements[target_error_context.error_statement_number-1] if 1 <= target_error_context.error_statement_number <= len(corrected_statements) else "",
                 "Type": "CORRECTED ERROR STATEMENT"},
                {"Statement Number": target_error_context.after_statement_number,
                 "Statement": corrected_statements[target_error_context.after_statement_number-1] if 1 <= target_error_context.after_statement_number <= len(corrected_statements) else "",
                 "Type": "After Error"}
            ]

            # Filter out empty statements
            data = [d for d in data if d["Statement Number"] > 0 and d["Statement"]]

            df = pd.DataFrame(data)

            # Get output directory
            output_path = get_output_path()

            # Get filename with iteration number (shortened)
            excel_filename = get_iteration_filename("corrected_statements.xlsx")

            # Save to Excel
            excel_path = os.path.join(output_path, excel_filename)
            df.to_excel(excel_path, index=False, sheet_name="Corrected Statements")

            print(f"💾 Saved corrected statements to {os.path.abspath(excel_path)}")

        except Exception as e:
            print(f"⚠️ Could not save corrected statements: {e}")

    def replaceTargetStatement(self, state: WorkflowState) -> Dict[str, Any]:
        """Pass through AI corrections without doing code replacement - replacement happens in targetcode_deployment."""
        try:
            print("🔄 Passing AI corrections to deployment...")

            # Get the AI corrections and original target statements
            ai_corrections = getattr(state, 'ai_corrections', None)
            original_target_statements = getattr(state, 'original_target_statements', None)

            # Get current iteration count
            iteration_count = getattr(state, 'iteration_count', 1)
            print(f"📊 Passing AI corrections for iteration {iteration_count}")

            if not ai_corrections:
                print("⚠️ No AI corrections provided")
                return {
                    "iteration_count": iteration_count  # Preserve iteration count
                }

            print(f"📊 Passing {len(ai_corrections)} AI corrections to deployment")

            return {
                "ai_corrections": ai_corrections,
                "original_target_statements": original_target_statements,
                "iteration_count": iteration_count  # Preserve iteration count
            }

        except Exception as e:
            print(f"❌ Error in passing corrected statements: {str(e)}")
            # Get current iteration count even in case of error
            iteration_count = getattr(state, 'iteration_count', 1)
            return {
                "iteration_count": iteration_count  # Preserve iteration count
            }

    def saveUpdatedTargetCode(self, updated_target_code: str):
        """Save the updated target code to a file."""
        try:
            # Get output directory
            output_path = get_output_path()

            # Get filename with iteration number
            sql_filename = get_iteration_filename("updated_target_code.sql")

            # Save to file
            file_path = os.path.join(output_path, sql_filename)
            with open(file_path, "w") as f:
                f.write(updated_target_code)

            print(f"💾 Saved updated target code to {os.path.abspath(file_path)}")

        except Exception as e:
            print(f"⚠️ Could not save updated target code: {e}")

    def targetcode_deployment(self, state: WorkflowState) -> Dict[str, Any]:
        """Apply AI corrections to target code and deploy to PostgreSQL database."""
        try:
            print("🚀 Starting code replacement and deployment to PostgreSQL...")

            # Get AI corrections and original target statements
            ai_corrections = getattr(state, 'ai_corrections', None)
            original_target_statements = getattr(state, 'original_target_statements', None)
            updated_target_code = getattr(state, 'updated_target_code', None)

            # Get current iteration count
            iteration_count = getattr(state, 'iteration_count', 1)
            print(f"📊 Deployment attempt for iteration {iteration_count}")

            # If we have AI corrections, apply them to create corrected statements
            if ai_corrections and original_target_statements:
                print("🔄 Applying AI corrections to target code...")
                print(f"📊 Applying {len(ai_corrections)} AI corrections to {len(original_target_statements)} statements")

                # Create a copy of the original target statements
                corrected_target_statements = original_target_statements.copy()

                # Apply ONLY the error statement correction (not before/after statements)
                target_error_context = getattr(state, 'target_error_context', None)
                error_stmt_num = target_error_context.error_statement_number if target_error_context else None

                if error_stmt_num:
                    # Find the corrected error statement directly instead of looping through all corrections
                    corrected_error_stmt = None
                    for correction in ai_corrections:
                        if correction.statement_number == error_stmt_num and correction.statement_type == "error_statement":
                            corrected_error_stmt = correction.corrected_statement
                            break

                    if corrected_error_stmt:
                        # Validate statement number bounds
                        if 1 <= error_stmt_num <= len(corrected_target_statements):
                            print(f"✏️ Directly replacing statement #{error_stmt_num} (error_statement)")
                            corrected_target_statements[error_stmt_num-1] = corrected_error_stmt
                            print(f"✅ Successfully replaced error statement #{error_stmt_num}")
                        else:
                            print(f"⚠️ Invalid error statement number: {error_stmt_num} (valid range: 1-{len(corrected_target_statements)})")
                    else:
                        print(f"⚠️ No corrected statement found for error statement #{error_stmt_num}")
                        # Log available corrections for debugging
                        available_corrections = [f"#{c.statement_number}({c.statement_type})" for c in ai_corrections]
                        print(f"🔍 Available corrections: {available_corrections}")
                else:
                    print("⚠️ No error statement number available from target error context")

                # Join the corrected statements to create the updated target code
                updated_target_code = "\n".join(corrected_target_statements)
                print(f"📝 Generated updated target code with {len(corrected_target_statements)} statements")

                # Save the updated target code to a file
                self.saveUpdatedTargetCode(updated_target_code)

                # Save corrected statements to Excel for tracking
                target_error_context = getattr(state, 'target_error_context', None)
                if target_error_context:
                    self.saveCorrectedStatementsToExcel(corrected_target_statements, target_error_context)

                print(f"✅ AI corrections applied successfully")

            if not updated_target_code:
                print("⚠️ No updated target code available for deployment")
                return {
                    "deployment_successful": False,
                    "iteration_count": iteration_count  # Preserve iteration count
                }

            print(f"📊 Deploying updated target code to PostgreSQL")

            # Deploy to PostgreSQL database
            deployment_successful, error_message = self.deployToPostgres(updated_target_code)

            # Save deployment results
            self.saveDeploymentResults(deployment_successful, error_message)

            if deployment_successful:
                print(f"✅ Code deployed successfully to PostgreSQL")
            else:
                print(f"❌ Code deployment to PostgreSQL failed: {error_message}")

            return {
                "deployment_successful": deployment_successful,
                "error_message": error_message if not deployment_successful else None,
                "updated_target_code": updated_target_code,  # Include updated code in return
                "iteration_count": iteration_count  # Preserve iteration count
            }

        except Exception as e:
            print(f"❌ Error in code deployment: {str(e)}")
            # Get current iteration count even in case of error
            iteration_count = getattr(state, 'iteration_count', 1)
            return {
                "deployment_successful": False,
                "error_message": str(e),
                "iteration_count": iteration_count  # Preserve iteration count
            }

    def deployToPostgres(self, updated_target_code: str) -> tuple:
        """Deploy the code to PostgreSQL database and check for errors."""
        try:
            # print(f"🔌 Connecting to PostgreSQL database...")

            # Import required modules
            try:
                # Get PostgreSQL connection parameters from environment variables
                conn_params = {
                    "host": os.getenv("PG_HOST"),
                    "port": os.getenv("PG_PORT"),
                    "database": os.getenv("PG_DATABASE"),
                    "user": os.getenv("PG_USER"),
                    "password": os.getenv("PG_PASSWORD")
                }

                print(f"🔌 Connecting to PostgreSQL database...")

                # Connect to PostgreSQL
                conn = psycopg2.connect(**conn_params)
                conn.autocommit = False

                # Create a cursor
                cursor = conn.cursor()

                # Execute the SQL code
                cursor.execute(updated_target_code)

                # Commit the transaction
                conn.commit()

                # Close the cursor and connection
                cursor.close()
                conn.close()

                print("✅ SQL code executed successfully")
                return True, None

            except Exception as e:
                # Handle PostgreSQL errors
                error_message = f"PostgreSQL Error: {e.pgerror}" if hasattr(e, 'pgerror') else str(e)
                print(f"❌ PostgreSQL execution failed: {error_message}")

                # Rollback the transaction if needed
                if 'conn' in locals() and conn:
                    conn.rollback()
                    conn.close()

                return False, error_message

        except Exception as e:
            print(f"❌ Deployment error: {str(e)}")
            return False, str(e)





    def saveDeploymentResults(self, deployment_successful: bool, error_message: str = None):
        """Save the deployment results to an Excel file."""
        try:
            # Create data for DataFrame
            data = [
                {"Status": "Success" if deployment_successful else "Failed",
                 "Timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S"),
                 "Error Message": error_message if error_message else "N/A"}
            ]

            df = pd.DataFrame(data)

            # Get output directory
            output_path = get_output_path()

            # Get filename with iteration number
            excel_filename = get_iteration_filename("deployment_results.xlsx")

            # Save to Excel
            excel_path = os.path.join(output_path, excel_filename)
            df.to_excel(excel_path, index=False, sheet_name="Deployment Results")

            print(f"💾 Saved deployment results to {os.path.abspath(excel_path)}")

        except Exception as e:
            print(f"⚠️ Could not save deployment results: {e}")

    def saveConversionValidationToExcel(self, source_context: ErrorContext, target_error_context: ErrorContext, corrected_error_stmt: str, is_correct: bool, explanation: str):
        """Save conversion validation results to Excel file."""
        try:
            # Create data for DataFrame with PostgreSQL-specific indicators
            data = [
                {
                    "Validation Result": "PASSED" if is_correct else "FAILED",
                    "Explanation": explanation,
                    "Source Error Statement Number": source_context.error_statement_number,
                    "Source Error Statement": source_context.error_statement,
                    "Source Type": "PostgreSQL-Specific" if source_context.error_statement_number == 0 else "Oracle Source",
                    "Target Error Statement Number": target_error_context.error_statement_number,
                    "Original Target Error Statement": target_error_context.error_statement,
                    "Corrected Target Error Statement": corrected_error_stmt,
                    "Target Type": "PostgreSQL Target",
                    "Timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            ]

            df = pd.DataFrame(data)

            # Get output directory
            output_path = get_output_path()

            # Get filename with iteration number (shortened)
            excel_filename = get_iteration_filename("conversion_validation.xlsx")

            # Save to Excel
            excel_path = os.path.join(output_path, excel_filename)
            df.to_excel(excel_path, index=False, sheet_name="Conversion Validation")

            print(f"💾 Saved conversion validation results to {os.path.abspath(excel_path)}")

        except Exception as e:
            print(f"⚠️ Could not save conversion validation results: {e}")

    def validateConversionWithAI(self, source_context: ErrorContext, target_error_context: ErrorContext, corrected_error_stmt: str, target_statements: List[str] = None) -> Dict[str, Any]:
        """Use AI to validate if the converted statement is correct compared to the source statement using structured output."""
        print(f"🧠 Using AI to validate conversion...")

        # Create prompt for AI using src_tgt validation approach
        prompt = create_src_tgt_validation_prompt(source_context, target_error_context, corrected_error_stmt, target_statements)

        # Use structured output for reliable parsing
        structured_llm = self.llm.client.with_structured_output(ValidationOutput)
        ai_result = structured_llm.invoke(prompt)

        is_correct = ai_result.is_correct
        explanation = ai_result.explanation
        confidence = ai_result.confidence

        # Create result
        result = {
            'is_correct': is_correct,
            'explanation': explanation,
            'confidence': confidence
        }

        print(f"🎯 AI validation result: correct={is_correct}, confidence={confidence}")
        print(f"📝 Explanation: {explanation}")

        return result

    def validate_conversion(self, state: WorkflowState) -> Dict[str, Any]:
        """Validate if the AI-generated corrections are correct compared to the source statement."""
        try:
            print("🔍 Starting conversion validation...")

            # Get the source context, target error context, and AI corrections
            source_context = state.source_context
            target_error_context = state.target_error_context
            ai_corrections = getattr(state, 'ai_corrections', None)
            original_target_statements = getattr(state, 'original_target_statements', None)
            target_specific_optimization = getattr(state, 'target_specific_optimization', False)

            # Get current conversion attempts count
            conversion_attempts = getattr(state, 'conversion_attempts', 0) + 1
            print(f"📊 Conversion validation attempt {conversion_attempts}")

            if target_specific_optimization:
                print("🚀 OPTIMIZATION: Validating target-specific conversion (PostgreSQL expertise applied)")
            else:
                print("🔄 STANDARD: Validating source-to-target conversion")

            if not source_context:
                print("⚠️ No source context to validate against")
                return {
                    "conversion_successful": False,
                    "conversion_attempts": conversion_attempts
                }

            if not target_error_context:
                print("⚠️ No target error context to validate")
                return {
                    "conversion_successful": False,
                    "conversion_attempts": conversion_attempts
                }

            if not ai_corrections:
                print("⚠️ No AI corrections to validate")
                return {
                    "conversion_successful": False,
                    "conversion_attempts": conversion_attempts
                }

            if not original_target_statements:
                print("⚠️ No original target statements for validation")
                return {
                    "conversion_successful": False,
                    "conversion_attempts": conversion_attempts
                }

            print(f"📊 Validating {len(ai_corrections)} AI corrections for error statement #{target_error_context.error_statement_number}")

            # Find the corrected error statement from AI corrections
            error_stmt_num = target_error_context.error_statement_number
            corrected_error_stmt = None

            for correction in ai_corrections:
                if correction.statement_number == error_stmt_num:
                    corrected_error_stmt = correction.corrected_statement
                    break

            if not corrected_error_stmt:
                print(f"⚠️ AI did not return correction for error statement #{error_stmt_num}")
                print(f"🔍 AI returned corrections for: {[f'#{c.statement_number}({c.statement_type})' for c in ai_corrections]}")
                print(f"❌ AI must return the exact statement number {error_stmt_num} for error_statement")
                return {
                    "conversion_successful": False,
                    "conversion_attempts": conversion_attempts
                }

            # Use single AI validation - simple and effective
            validation_result = self.validateConversionWithAI(
                source_context,
                target_error_context,
                corrected_error_stmt,
                original_target_statements
            )

            # Extract validation success and explanation
            conversion_successful = validation_result.get('is_correct', False)
            explanation = validation_result.get('explanation', 'No explanation provided')
            confidence = validation_result.get('confidence', 0.0)

            # Save validation results to Excel
            self.saveConversionValidationToExcel(
                source_context,
                target_error_context,
                corrected_error_stmt,
                conversion_successful,
                explanation
            )

            if conversion_successful:
                print(f"✅ Conversion validated successfully with confidence {confidence}")
                return {
                    "conversion_successful": True,
                    "conversion_attempts": conversion_attempts,
                    "conversion_feedback": None  # Clear feedback on success
                }
            else:
                print(f"❌ Conversion validation failed with confidence {confidence}")
                print(f"📝 Conversion feedback: {explanation}")

                # Return the result along with the updated attempts count and feedback
                return {
                    "conversion_successful": False,
                    "conversion_attempts": conversion_attempts,
                    "conversion_feedback": explanation  # Store failure feedback
                }

        except Exception as e:
            print(f"❌ Error in conversion validation: {str(e)}")
            # Get current conversion attempts count even in case of error
            conversion_attempts = getattr(state, 'conversion_attempts', 0) + 1
            return {
                "conversion_successful": False,
                "conversion_attempts": conversion_attempts,
                "conversion_feedback": f"Validation error: {str(e)}"  # Store error as feedback
            }

    def deployment_status(self, state: WorkflowState) -> Dict[str, Any]:
        """Check the deployment status and determine if the workflow should end."""
        try:
            print("🔄 Checking deployment status...")

            # Get the deployment status
            deployment_successful = state.deployment_successful
            error_message = getattr(state, 'error_message', None)
            updated_target_code = getattr(state, 'updated_target_code', None)
            source_code = getattr(state, 'source_code', None)
            source_statements = getattr(state, 'source_statements', [])

            # Update the global iteration counter when deployment fails
            global CURRENT_ITERATION

            # Log the current iteration count
            print(f"📊 Current iteration count: {CURRENT_ITERATION}")

            if deployment_successful:
                print(f"📊 Deployment successful on iteration {CURRENT_ITERATION}")
            else:
                # Increment the global iteration counter for the next run
                CURRENT_ITERATION += 1
                print(f"📊 Deployment failed on iteration {CURRENT_ITERATION-1}: {error_message}")
                print(f"🔄 Starting iteration {CURRENT_ITERATION}")
                print(f"❌ Deployment failed - going back to split statements")
                print(f"📊 Global iteration counter incremented to: {CURRENT_ITERATION}")

                # If deployment failed, prepare for next iteration
                # Use the deployment error as the new input error message
                # Use the updated target code as the new target code
                # Keep the original source code as the source code
                # Increment the iteration count
                return {
                    "deployment_successful": deployment_successful,
                    "error_message": error_message,
                    "deployment_error": error_message,  # Use as input for next iteration
                    "target_code": updated_target_code,  # Use updated code as new target
                    "source_code": source_code,  # Keep original source code
                    "source_statements": source_statements,  # Preserve source statements for reuse
                    "iteration_count": 1  # Keep iteration count at 1 in state
                }

        except Exception as e:
            print(f"❌ Error in deployment status check: {str(e)}")
            return {
                "deployment_successful": False,
                "error_message": str(e)
            }


